import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export const exportToPDF = async (elementId: string, filename: string) => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('Element not found for PDF export');
    }

    // Temporarily modify styles for better PDF output
    const originalStyles = element.style.cssText;
    const originalOverflow = element.style.overflow;

    // Ensure content is fully visible for capture
    element.style.background = 'white';
    element.style.color = 'black';
    element.style.overflow = 'visible';
    element.style.height = 'auto';
    element.style.maxHeight = 'none';

    // Wait for any dynamic content to load
    await new Promise(resolve => setTimeout(resolve, 100));

    const canvas = await html2canvas(element, {
      scale: 1.5, // Good balance between quality and performance
      useCORS: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: element.scrollWidth,
      height: element.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      allowTaint: true
    });

    const imgData = canvas.toDataURL('image/png', 0.95);
    const pdf = new jsPDF('p', 'mm', 'a4');

    // A4 dimensions in mm
    const pdfWidth = pdf.internal.pageSize.getWidth(); // 210mm
    const pdfHeight = pdf.internal.pageSize.getHeight(); // 297mm

    // Add margins for better readability
    const margin = 10; // 10mm margin
    const contentWidth = pdfWidth - (margin * 2);
    const contentHeight = pdfHeight - (margin * 2);

    // Calculate scaling to fit content width
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const widthRatio = contentWidth / (imgWidth * 0.264583); // Convert pixels to mm
    const scaledHeight = (imgHeight * 0.264583) * widthRatio;

    // Calculate how many pages we need
    const totalPages = Math.ceil(scaledHeight / contentHeight);

    for (let page = 0; page < totalPages; page++) {
      if (page > 0) {
        pdf.addPage();
      }

      // Calculate the portion of the image for this page
      const yOffset = page * contentHeight;
      const remainingHeight = scaledHeight - yOffset;
      const pageContentHeight = Math.min(contentHeight, remainingHeight);

      // Calculate source coordinates in the original image
      const srcY = (yOffset / widthRatio) / 0.264583; // Convert back to pixels
      const srcHeight = (pageContentHeight / widthRatio) / 0.264583;

      if (pageContentHeight > 0) {
        // Add CourseCraft header on each page
        pdf.setFontSize(8);
        pdf.setTextColor(100, 100, 100);
        pdf.text('Generated by CourseCraft - AI-Powered Curriculum Generator', margin, margin - 2);

        // Add the image portion for this page
        pdf.addImage(
          imgData,
          'PNG',
          margin,
          margin,
          contentWidth,
          pageContentHeight,
          undefined,
          'FAST',
          0,
          -srcY * 0.264583 * widthRatio
        );

        // Add page number
        pdf.setFontSize(8);
        pdf.setTextColor(100, 100, 100);
        pdf.text(`Page ${page + 1} of ${totalPages}`, pdfWidth - margin - 20, pdfHeight - 5);
      }
    }

    // Restore original styles
    element.style.cssText = originalStyles;
    element.style.overflow = originalOverflow;

    pdf.save(filename);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF. Please try again.');
  }
};