import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export const exportToPDF = async (elementId: string, filename: string) => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('Element not found for PDF export');
    }

    // Create a clone of the element to avoid modifying the original
    const clone = element.cloneNode(true) as HTMLElement;
    document.body.appendChild(clone);

    // Style the clone for PDF export
    clone.style.position = 'absolute';
    clone.style.left = '-9999px';
    clone.style.top = '0';
    clone.style.width = '800px'; // Fixed width for better layout
    clone.style.background = 'white';
    clone.style.color = 'black';
    clone.style.padding = '20px';
    clone.style.fontFamily = 'Arial, sans-serif';
    clone.style.fontSize = '12px';
    clone.style.lineHeight = '1.5';
    clone.style.boxSizing = 'border-box';

    // Ensure all tables have proper styling
    const tables = clone.querySelectorAll('table');
    tables.forEach(table => {
      table.style.width = '100%';
      table.style.borderCollapse = 'collapse';
      table.style.marginBottom = '15px';

      const cells = table.querySelectorAll('th, td');
      cells.forEach(cell => {
        (cell as HTMLElement).style.border = '1px solid #ddd';
        (cell as HTMLElement).style.padding = '8px';
        (cell as HTMLElement).style.textAlign = 'left';
      });

      const headers = table.querySelectorAll('th');
      headers.forEach(header => {
        (header as HTMLElement).style.backgroundColor = '#f2f2f2';
        (header as HTMLElement).style.fontWeight = 'bold';
      });
    });

    // Wait for styles to apply
    await new Promise(resolve => setTimeout(resolve, 500));

    // Create PDF
    const pdf = new jsPDF('p', 'pt', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 40;

    // Add logo and header to first page
    pdf.setFontSize(10);
    pdf.setTextColor(0, 0, 0);
    pdf.text('CourseCraft - AI-Powered Curriculum Generator', margin, 30);

    // Function to add content to PDF
    const addTextToPDF = (text: string, x: number, y: number, options: any = {}) => {
      const defaultOptions = {
        align: 'left',
        maxWidth: pageWidth - (2 * margin)
      };
      const mergedOptions = { ...defaultOptions, ...options };
      return pdf.text(text, x, y, mergedOptions);
    };

    // Extract text content from the clone
    const extractTextContent = (element: HTMLElement): string[] => {
      const result: string[] = [];

      // Process headings
      const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach(heading => {
        const level = parseInt(heading.tagName.substring(1));
        const prefix = '•'.repeat(level);
        result.push(`${prefix} ${heading.textContent?.trim()}`);
        result.push('');
      });

      // Process paragraphs
      const paragraphs = element.querySelectorAll('p');
      paragraphs.forEach(para => {
        result.push(para.textContent?.trim() || '');
        result.push('');
      });

      // Process lists
      const lists = element.querySelectorAll('ul, ol');
      lists.forEach(list => {
        const items = list.querySelectorAll('li');
        items.forEach(item => {
          result.push(`• ${item.textContent?.trim()}`);
        });
        result.push('');
      });

      // Process tables
      const tables = element.querySelectorAll('table');
      tables.forEach(table => {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
          const cells = row.querySelectorAll('th, td');
          const rowText = Array.from(cells)
            .map(cell => cell.textContent?.trim())
            .join(' | ');
          result.push(rowText);
        });
        result.push('');
      });

      return result;
    };

    // Get text content
    const textContent = extractTextContent(clone);

    // Add content to PDF
    let y = 60;
    const lineHeight = 14;
    let pageNum = 1;

    for (const line of textContent) {
      // Check if we need a new page
      if (y > pageHeight - margin) {
        pdf.addPage();
        pageNum++;
        y = 60;

        // Add header to new page
        pdf.setFontSize(10);
        pdf.setTextColor(0, 0, 0);
        pdf.text('CourseCraft - AI-Powered Curriculum Generator', margin, 30);
      }

      if (line.trim() === '') {
        y += lineHeight / 2;
        continue;
      }

      // Check if line is a heading
      if (line.startsWith('•')) {
        const level = line.indexOf(' ');
        pdf.setFontSize(16 - level);
        pdf.setTextColor(0, 0, 0);
        addTextToPDF(line.substring(level + 1), margin, y);
        y += lineHeight + 4;
      } else if (line.includes(' | ')) {
        // Table row
        const cells = line.split(' | ');
        const cellWidth = (pageWidth - 2 * margin) / cells.length;

        cells.forEach((cell, index) => {
          addTextToPDF(cell, margin + (index * cellWidth), y, { maxWidth: cellWidth - 5 });
        });

        y += lineHeight;

        // Add a line after the row
        pdf.setDrawColor(200, 200, 200);
        pdf.line(margin, y - 2, pageWidth - margin, y - 2);
        y += 4;
      } else {
        // Regular text
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);

        // Split long lines
        const textLines = pdf.splitTextToSize(line, pageWidth - 2 * margin);
        for (const textLine of textLines) {
          addTextToPDF(textLine, margin, y);
          y += lineHeight;
        }
      }
    }

    // Add page numbers
    const totalPages = pdf.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      pdf.text(`Page ${i} of ${totalPages}`, pageWidth - margin - 60, pageHeight - 20);
    }

    // Remove the clone
    document.body.removeChild(clone);

    // Save the PDF
    pdf.save(filename);

  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF. Please try again.');
  }
};