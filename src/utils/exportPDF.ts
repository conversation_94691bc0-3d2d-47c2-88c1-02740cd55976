import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export const exportToPDF = async (elementId: string, filename: string) => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('Element not found for PDF export');
    }

    // Temporarily modify styles for better PDF output
    const originalStyles = element.style.cssText;
    element.style.background = 'white';
    element.style.color = 'black';

    const canvas = await html2canvas(element, {
      scale: 1.5, // Reduced scale for better fitting
      useCORS: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: element.scrollWidth,
      height: element.scrollHeight,
      scrollX: 0,
      scrollY: 0
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4');

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // Better ratio calculation for proper fitting
    const ratio = Math.min((pdfWidth - 20) / imgWidth, (pdfHeight - 40) / imgHeight); // Leave margins
    const scaledWidth = imgWidth * ratio;
    const scaledHeight = imgHeight * ratio;
    const imgX = (pdfWidth - scaledWidth) / 2;

    // Calculate how many pages we need
    const pageContentHeight = pdfHeight - 40; // Leave space for header/footer
    const totalPDFPages = Math.ceil(scaledHeight / pageContentHeight);

    for (let i = 0; i < totalPDFPages; i++) {
      if (i > 0) {
        pdf.addPage();
      }

      // Add CourseCraft header on each page
      addCourseCraftHeader(pdf, i + 1, totalPDFPages);

      const srcY = i * (pageContentHeight / ratio);
      const srcHeight = Math.min(imgHeight - srcY, pageContentHeight / ratio);

      if (srcHeight > 0) {
        pdf.addImage(
          imgData,
          'PNG',
          imgX,
          30, // Start below header
          scaledWidth,
          srcHeight * ratio,
          undefined,
          'FAST',
          0,
          -srcY * ratio
        );
      }

      // Add footer
      addCourseCraftFooter(pdf);
    }

    // Restore original styles
    element.style.cssText = originalStyles;

    pdf.save(filename);
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF. Please try again.');
  }
};

// Helper function to add CourseCraft header
const addCourseCraftHeader = (pdf: jsPDF, pageNum: number, totalPages: number) => {
  const pdfWidth = pdf.internal.pageSize.getWidth();

  // Header background
  pdf.setFillColor(6, 182, 212); // Cyan color
  pdf.rect(0, 0, pdfWidth, 25, 'F');

  // Logo placeholder (you can replace with actual logo if needed)
  pdf.setFillColor(255, 255, 255);
  pdf.circle(15, 12.5, 8, 'F');
  pdf.setFontSize(8);
  pdf.setTextColor(6, 182, 212);
  pdf.text('LOGO', 11, 15);

  // CourseCraft branding
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(255, 255, 255);
  pdf.text('CourseCraft', 30, 15);

  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('AI-Powered Curriculum Generator', 30, 20);

  // Page number
  pdf.setFontSize(10);
  pdf.setTextColor(255, 255, 255);
  pdf.text(`Page ${pageNum} of ${totalPages}`, pdfWidth - 40, 15);

  // Reset colors
  pdf.setTextColor(0, 0, 0);
};

// Helper function to add CourseCraft footer
const addCourseCraftFooter = (pdf: jsPDF) => {
  const pdfWidth = pdf.internal.pageSize.getWidth();
  const pdfHeight = pdf.internal.pageSize.getHeight();

  // Footer line
  pdf.setDrawColor(200, 200, 200);
  pdf.line(10, pdfHeight - 15, pdfWidth - 10, pdfHeight - 15);

  // Footer text
  pdf.setFontSize(8);
  pdf.setTextColor(100, 100, 100);
  pdf.text('Generated by CourseCraft AI-Powered Curriculum Generator', 10, pdfHeight - 8);
  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, pdfWidth - 60, pdfHeight - 8);
};