import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

export const exportToPDF = async (elementId: string, filename: string) => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('Element not found for PDF export');
    }

    // Extract course data from the DOM
    const courseData = extractCourseData(element);
    
    // Create PDF with professional formatting
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 15;
    
    let yPosition = margin;

    // Add header with logo and title
    addHeader(pdf, courseData, margin, yPosition);
    yPosition += 30;
    
    // Add course information table
    yPosition = addCourseInfoTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add course outcomes table
    yPosition = addCourseOutcomesTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add units table
    yPosition = addUnitsTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add assessment pattern table
    yPosition = addAssessmentTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add textbooks table
    yPosition = addTextbooksTable(pdf, courseData, margin, yPosition);
    
    // Add page numbers
    addPageNumbers(pdf);
    
    // Save the PDF
    pdf.save(filename);
    
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF. Please try again.');
  }
};

// Function to extract course data from DOM
function extractCourseData(element: HTMLElement) {
  const data: any = {
    title: '',
    code: '',
    credits: '',
    department: '',
    semester: '',
    outcomes: [],
    units: [],
    assessment: {},
    textbooks: [],
    references: []
  };
  
  // Extract course title and basic info
  const titleElement = element.querySelector('h1, .course-title');
  if (titleElement) {
    data.title = titleElement.textContent?.trim() || '';
  }
  
  // Extract course code, credits, etc. from the content
  const textContent = element.textContent || '';
  
  // Extract course code (pattern: letters followed by numbers)
  const codeMatch = textContent.match(/([A-Z]{2,4}\d{3,4})/);
  if (codeMatch) {
    data.code = codeMatch[1];
  }
  
  // Extract credits
  const creditsMatch = textContent.match(/(\d+)\s*credits?/i);
  if (creditsMatch) {
    data.credits = creditsMatch[1];
  }
  
  // Extract course outcomes
  const outcomes = [];
  const coMatches = textContent.matchAll(/CO(\d+)[:\-\s]*([^.]+\.)/g);
  for (const match of coMatches) {
    outcomes.push({
      id: `CO${match[1]}`,
      description: match[2].trim()
    });
  }
  data.outcomes = outcomes;
  
  // Extract units
  const units = [];
  const unitMatches = textContent.matchAll(/Unit\s*(\d+)[:\-\s]*([^Unit]+?)(?=Unit\s*\d+|$)/gi);
  for (const match of unitMatches) {
    const unitContent = match[2].trim();
    const topics = unitContent.split(/[,;]/).map(t => t.trim()).filter(t => t.length > 0);
    units.push({
      number: match[1],
      title: `Unit ${match[1]}`,
      topics: topics.slice(0, 8) // Limit to 8 topics for better formatting
    });
  }
  data.units = units;
  
  return data;
}

// Function to add header
function addHeader(pdf: jsPDF, courseData: any, margin: number, y: number) {
  // Add CourseCraft logo/title
  pdf.setFontSize(16);
  pdf.setTextColor(0, 102, 204); // Blue color
  pdf.text('CourseCraft', margin, y);
  
  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);
  pdf.text('AI-Powered Curriculum Generator', margin, y + 6);
  
  // Add course title
  pdf.setFontSize(18);
  pdf.setTextColor(0, 0, 0);
  const title = courseData.title || 'Course Syllabus';
  pdf.text(title, margin, y + 20);
}

// Function to add course information table
function addCourseInfoTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const tableData = [
    ['Course Code', courseData.code || 'CS301'],
    ['Course Title', courseData.title || 'Course Title'],
    ['Credits', courseData.credits || '3'],
    ['Department', courseData.department || 'Computer Science'],
    ['Semester', courseData.semester || '3']
  ];
  
  autoTable(pdf, {
    startY: y,
    head: [['Course Information', '']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 10,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 60, fontStyle: 'bold' },
      1: { cellWidth: 120 }
    },
    margin: { left: margin, right: margin }
  });
  
  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add course outcomes table
function addCourseOutcomesTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const outcomes = courseData.outcomes.length > 0 ? courseData.outcomes : [
    {
      id: 'CO1',
      description: 'Understand fundamental concepts, principles, and theoretical foundations of the subject matter including core definitions, basic terminology, and foundational knowledge required for advanced study.'
    },
    {
      id: 'CO2',
      description: 'Apply theoretical knowledge and concepts to solve practical problems, implement solutions, and demonstrate hands-on skills in real-world scenarios and case studies.'
    },
    {
      id: 'CO3',
      description: 'Analyze complex problems, evaluate different approaches and methodologies, compare alternative solutions, and make informed decisions based on critical thinking.'
    },
    {
      id: 'CO4',
      description: 'Design and develop innovative solutions, create new systems or processes, and synthesize knowledge from multiple sources to address complex challenges.'
    },
    {
      id: 'CO5',
      description: 'Demonstrate professional skills, ethical responsibility, effective communication, teamwork abilities, and lifelong learning commitment in the field.'
    }
  ];

  const tableData = outcomes.map((outcome: any) => [
    outcome.id,
    outcome.description
  ]);

  autoTable(pdf, {
    startY: y,
    head: [['Course Outcome', 'Learning Objective Description']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 4,
      valign: 'top'
    },
    columnStyles: {
      0: {
        cellWidth: 25,
        halign: 'center',
        fontStyle: 'bold',
        fillColor: [240, 248, 255]
      },
      1: {
        cellWidth: 155,
        cellPadding: 5
      }
    },
    margin: { left: margin, right: margin },
    styles: {
      cellPadding: 4,
      fontSize: 9,
      lineColor: [200, 200, 200],
      lineWidth: 0.5
    }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add units table
function addUnitsTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const units = courseData.units.length > 0 ? courseData.units : [
    {
      number: '1',
      title: 'Introduction and Fundamentals',
      topics: [
        'Basic concepts and definitions',
        'Historical background and evolution',
        'Fundamental principles and theories',
        'Overview of applications and scope',
        'Introduction to key terminology'
      ]
    },
    {
      number: '2',
      title: 'Core Concepts and Methodologies',
      topics: [
        'Advanced theoretical frameworks',
        'Key methodologies and approaches',
        'Implementation techniques and strategies',
        'Comparative analysis of methods',
        'Best practices and standards'
      ]
    },
    {
      number: '3',
      title: 'Practical Applications',
      topics: [
        'Real-world case studies and examples',
        'Industry applications and use cases',
        'Hands-on implementation projects',
        'Problem-solving techniques',
        'Performance evaluation methods'
      ]
    },
    {
      number: '4',
      title: 'Advanced Topics and Research',
      topics: [
        'Cutting-edge research areas',
        'Emerging trends and technologies',
        'Complex problem-solving approaches',
        'Innovation and future directions',
        'Research methodologies and tools'
      ]
    },
    {
      number: '5',
      title: 'Integration and Evaluation',
      topics: [
        'System integration techniques',
        'Quality assurance and testing',
        'Performance optimization strategies',
        'Industry standards and compliance',
        'Project evaluation and assessment'
      ]
    }
  ];

  const tableData = units.map((unit: any) => [
    `Unit ${unit.number}`,
    unit.title,
    formatTopicsList(unit.topics)
  ]);

  autoTable(pdf, {
    startY: y,
    head: [['Unit', 'Unit Title', 'Topics Covered']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 4,
      valign: 'top'
    },
    columnStyles: {
      0: {
        cellWidth: 18,
        halign: 'center',
        fontStyle: 'bold',
        fillColor: [240, 248, 255]
      },
      1: {
        cellWidth: 52,
        fontStyle: 'bold',
        cellPadding: 4
      },
      2: {
        cellWidth: 110,
        cellPadding: 4
      }
    },
    margin: { left: margin, right: margin },
    styles: {
      cellPadding: 4,
      fontSize: 9,
      lineColor: [200, 200, 200],
      lineWidth: 0.5
    }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Helper function to format topics list
function formatTopicsList(topics: string[]): string {
  return topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n');
}

// Function to add assessment pattern table
function addAssessmentTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const assessmentData = [
    ['Internal Assessment', '40%'],
    ['Mid-term Examination', '20%'],
    ['End-term Examination', '60%'],
    ['Total', '100%']
  ];

  autoTable(pdf, {
    startY: y,
    head: [['Assessment Component', 'Weightage']],
    body: assessmentData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 10,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 120, fontStyle: 'bold' },
      1: { cellWidth: 60, halign: 'center' }
    },
    margin: { left: margin, right: margin }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add textbooks table
function addTextbooksTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const textbooks = [
    ['1', 'Introduction to Computer Science', 'John Smith', 'Tech Publications', '2023'],
    ['2', 'Advanced Programming Concepts', 'Jane Doe', 'Academic Press', '2022'],
    ['3', 'Software Engineering Principles', 'Bob Johnson', 'Engineering Books', '2023']
  ];

  const references = [
    ['1', 'Research Papers in Computer Science', 'Various Authors', 'IEEE', '2023'],
    ['2', 'Online Resources and Tutorials', 'Web Sources', 'Internet', '2023'],
    ['3', 'Industry Best Practices Guide', 'Professional Body', 'Standards Org', '2022']
  ];

  // Add textbooks table
  autoTable(pdf, {
    startY: y,
    head: [['S.No', 'Title', 'Author', 'Publisher', 'Year']],
    body: textbooks,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 15, halign: 'center' },
      1: { cellWidth: 70 },
      2: { cellWidth: 40 },
      3: { cellWidth: 35 },
      4: { cellWidth: 20, halign: 'center' }
    },
    margin: { left: margin, right: margin }
  });

  let newY = (pdf as any).lastAutoTable.finalY + 10;

  // Add references table
  autoTable(pdf, {
    startY: newY,
    head: [['S.No', 'Reference Title', 'Author', 'Publisher', 'Year']],
    body: references,
    theme: 'grid',
    headStyles: {
      fillColor: [102, 102, 102],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 15, halign: 'center' },
      1: { cellWidth: 70 },
      2: { cellWidth: 40 },
      3: { cellWidth: 35 },
      4: { cellWidth: 20, halign: 'center' }
    },
    margin: { left: margin, right: margin }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add page numbers
function addPageNumbers(pdf: jsPDF) {
  const totalPages = pdf.getNumberOfPages();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();

  for (let i = 1; i <= totalPages; i++) {
    pdf.setPage(i);
    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Page ${i} of ${totalPages}`, pageWidth - 30, pageHeight - 10);
  }
}
