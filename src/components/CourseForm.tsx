import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, GraduationCap, Clock, Users, Sparkles, Lightbulb, <PERSON>ting<PERSON>, Plus, Minus } from 'lucide-react';
import { CourseGenerationRequest } from '../types/course';
import { courseTemplates } from '../services/courseTemplates';
import { useAuth } from '../contexts/AuthContext';

interface CourseFormProps {
  onSubmit: (request: CourseGenerationRequest) => void;
  isLoading: boolean;
}

export const CourseForm: React.FC<CourseFormProps> = ({ onSubmit, isLoading }) => {
  const { user, getUserPreferences, updatePreferences } = useAuth();
  const [formData, setFormData] = useState<CourseGenerationRequest>({
    subject: '',
    level: 'undergraduate',
    semester: 1,
    duration: 16,
    courseType: 'theory',
    department: '',
    specialization: ''
  });
  const [selectedDomain, setSelectedDomain] = useState<string>('');
  const [preferences, setPreferences] = useState({ cosCount: 5, unitsCount: 5 });
  const [showPreferences, setShowPreferences] = useState(false);

  useEffect(() => {
    if (user) {
      loadUserPreferences();
    }
  }, [user]);

  const loadUserPreferences = async () => {
    try {
      const prefs = await getUserPreferences();
      setPreferences(prefs);
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const handleSavePreferences = async () => {
    try {
      await updatePreferences(preferences.cosCount, preferences.unitsCount);
      alert('Preferences saved successfully!');
    } catch (error) {
      console.error('Error saving preferences:', error);
      alert('Failed to save preferences');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleInputChange = (field: keyof CourseGenerationRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 max-w-4xl mx-auto border border-gray-700/50">
      <div className="flex items-center gap-4 mb-8">
        <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-4 rounded-2xl shadow-lg">
          <BookOpen className="w-8 h-8 text-white" />
        </div>
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
            Course Generation
          </h2>
          <p className="text-gray-400 text-lg">Create comprehensive course syllabi with AI assistance</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Domain Selection */}
        <div className="mb-8">
          <label className="block text-sm font-medium text-cyan-300 mb-4">
            <Lightbulb className="w-4 h-4 inline mr-2" />
            Select Domain (Optional - for better suggestions)
          </label>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
            {courseTemplates.map((template) => (
              <button
                key={template.domain}
                type="button"
                onClick={() => {
                  setSelectedDomain(template.domain);
                  if (!formData.department) {
                    handleInputChange('department', template.domain);
                  }
                }}
                className={`p-4 rounded-xl border transition-all duration-300 text-left ${
                  selectedDomain === template.domain
                    ? 'border-cyan-500 bg-cyan-500/10 text-cyan-300'
                    : 'border-gray-600/50 bg-gray-800/30 text-gray-400 hover:border-gray-500 hover:text-gray-300'
                }`}
              >
                <div className="font-medium text-sm">{template.domain}</div>
                <div className="text-xs mt-1 opacity-75">
                  {template.subjects.slice(0, 3).join(', ')}...
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Course Generation Preferences */}
        <div className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-cyan-300 flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Course Generation Preferences
            </h3>
            <button
              type="button"
              onClick={() => setShowPreferences(!showPreferences)}
              className="text-sm text-gray-400 hover:text-cyan-300 transition-colors"
            >
              {showPreferences ? 'Hide' : 'Show'} Settings
            </button>
          </div>

          {showPreferences && (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    Number of Course Outcomes (COs)
                  </label>
                  <div className="flex items-center gap-4">
                    <button
                      type="button"
                      onClick={() => setPreferences(prev => ({
                        ...prev,
                        cosCount: Math.max(1, prev.cosCount - 1)
                      }))}
                      className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <div className="bg-gray-700/50 px-6 py-2 rounded-lg border border-gray-600">
                      <span className="text-xl font-bold text-cyan-300">{preferences.cosCount}</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => setPreferences(prev => ({
                        ...prev,
                        cosCount: Math.min(10, prev.cosCount + 1)
                      }))}
                      className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                    <span className="text-gray-400 text-sm">Course Outcomes</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    Number of Units
                  </label>
                  <div className="flex items-center gap-4">
                    <button
                      type="button"
                      onClick={() => setPreferences(prev => ({
                        ...prev,
                        unitsCount: Math.max(1, prev.unitsCount - 1)
                      }))}
                      className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <div className="bg-gray-700/50 px-6 py-2 rounded-lg border border-gray-600">
                      <span className="text-xl font-bold text-cyan-300">{preferences.unitsCount}</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => setPreferences(prev => ({
                        ...prev,
                        unitsCount: Math.min(10, prev.unitsCount + 1)
                      }))}
                      className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                    <span className="text-gray-400 text-sm">Course Units</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={handleSavePreferences}
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 text-sm flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Save Preferences
                </button>
              </div>

              <div className="bg-gray-900/50 rounded-lg p-4 border-l-4 border-cyan-500">
                <p className="text-sm text-gray-300">
                  <strong>Note:</strong> These preferences will be used as defaults for all your course generations.
                  You can always modify them here or in your profile settings.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <BookOpen className="w-4 h-4 inline mr-2" />
              Subject/Course Name *
            </label>
            <input
              type="text"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              placeholder="e.g., Operating Systems, Machine Learning"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
              required
            />
            {selectedDomain && (
              <div className="mt-2">
                <p className="text-xs text-gray-400 mb-2">Popular subjects in {selectedDomain}:</p>
                <div className="flex flex-wrap gap-2">
                  {courseTemplates
                    .find(t => t.domain === selectedDomain)
                    ?.subjects.slice(0, 6)
                    .map((subject) => (
                      <button
                        key={subject}
                        type="button"
                        onClick={() => handleInputChange('subject', subject.charAt(0).toUpperCase() + subject.slice(1))}
                        className="px-3 py-1 text-xs bg-gray-700/50 text-gray-300 rounded-lg hover:bg-gray-600/50 transition-colors"
                      >
                        {subject.charAt(0).toUpperCase() + subject.slice(1)}
                      </button>
                    ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Department *
            </label>
            <input
              type="text"
              value={formData.department}
              onChange={(e) => handleInputChange('department', e.target.value)}
              placeholder="e.g., Computer Science and Engineering"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
              required
            />
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <GraduationCap className="w-4 h-4 inline mr-2" />
              Level
            </label>
            <select
              value={formData.level}
              onChange={(e) => handleInputChange('level', e.target.value as 'undergraduate' | 'postgraduate')}
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            >
              <option value="undergraduate">Undergraduate</option>
              <option value="postgraduate">Postgraduate</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <Users className="w-4 h-4 inline mr-2" />
              Semester
            </label>
            <select
              value={formData.semester}
              onChange={(e) => handleInputChange('semester', parseInt(e.target.value))}
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            >
              {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (
                <option key={sem} value={sem}>Semester {sem}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Course Type
            </label>
            <select
              value={formData.courseType}
              onChange={(e) => handleInputChange('courseType', e.target.value as 'theory' | 'practical' | 'theory-practical')}
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            >
              <option value="theory">Theory</option>
              <option value="practical">Practical</option>
              <option value="theory-practical">Theory + Practical</option>
            </select>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              <Clock className="w-4 h-4 inline mr-2" />
              Duration (weeks)
            </label>
            <input
              type="number"
              value={formData.duration}
              onChange={(e) => handleInputChange('duration', parseInt(e.target.value))}
              min="12"
              max="20"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-cyan-300 mb-3">
              Specialization (Optional)
            </label>
            <input
              type="text"
              value={formData.specialization}
              onChange={(e) => handleInputChange('specialization', e.target.value)}
              placeholder="e.g., Artificial Intelligence, Cybersecurity"
              className="w-full px-6 py-4 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-200 placeholder-gray-500 focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-300 backdrop-blur-sm"
            />
          </div>
        </div>

        <div className="pt-6">
          <button
            type="submit"
            disabled={isLoading || !formData.subject || !formData.department}
            className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold py-6 px-8 rounded-2xl hover:from-cyan-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-2xl hover:shadow-cyan-500/25 text-xl transform hover:scale-105 hover:-translate-y-1"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-3">
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <Sparkles className="w-5 h-5 animate-pulse" />
                Generating Course...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-3">
                <Sparkles className="w-5 h-5" />
                Generate Course Syllabus
              </div>
            )}
          </button>
        </div>
      </form>

      <div className="mt-8 p-6 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-2xl border border-cyan-500/20">
        <p className="text-sm text-cyan-200 leading-relaxed">
          <strong className="text-cyan-300">Note:</strong> This system generates AICTE-compliant course syllabi for Indian universities. 
          The generated content includes learning objectives, outcomes, assessment patterns, and resource recommendations 
          aligned with Indian academic standards.
        </p>
      </div>
    </div>
  );
};