import React, { useState } from 'react';
import {
  BookOpen,
  Target,
  Award,
  Clock,
  Users,
  FileText,
  ExternalLink,
  Download,
  Calendar,
  GraduationCap,
  Sparkles,
  TrendingUp,
  Briefcase,
  Zap,
  BarChart3,
  Star,
  X,
  Edit3,
  Save,
  Plus
} from 'lucide-react';
import { Course } from '../types/course';


interface CourseDisplayProps {
  course: Course;
  onExportPDF: () => void;
  onStartNew: () => void;
}

export const CourseDisplay: React.FC<CourseDisplayProps> = ({
  course,
  onExportPDF,
  onStartNew
}) => {
  const totalContactHours = course.contactHours.lecture + course.contactHours.tutorial + course.contactHours.practical;
  const [editingTextbook, setEditingTextbook] = useState<number | null>(null);
  const [editingReference, setEditingReference] = useState<number | null>(null);
  const [editingOutcome, setEditingOutcome] = useState<string | null>(null);
  const [editableTextbooks, setEditableTextbooks] = useState(course.textBooks);
  const [editableReferences, setEditableReferences] = useState(course.referenceBooks);
  const [editableOutcomes, setEditableOutcomes] = useState(course.outcomes);
  const [newBook, setNewBook] = useState({
    title: '',
    authors: [''],
    publisher: '',
    year: new Date().getFullYear(),
    isbn: '',
    edition: ''
  });

  const [newOutcome, setNewOutcome] = useState({
    id: '',
    description: '',
    bloomsLevel: 'Remember'
  });

  const handleEditTextbook = (index: number) => {
    const book = editableTextbooks[index];
    setNewBook({
      title: book.title,
      authors: book.authors,
      publisher: book.publisher,
      year: book.year,
      isbn: book.isbn || '',
      edition: book.edition || ''
    });
    setEditingTextbook(index);
  };

  const handleEditReference = (index: number) => {
    const book = editableReferences[index];
    setNewBook({
      title: book.title,
      authors: book.authors,
      publisher: book.publisher,
      year: book.year,
      isbn: book.isbn || '',
      edition: book.edition || ''
    });
    setEditingReference(index);
  };

  const handleSaveTextbook = () => {
    if (editingTextbook !== null) {
      const updatedBooks = [...editableTextbooks];
      updatedBooks[editingTextbook] = {
        ...newBook,
        authors: newBook.authors.filter(author => author.trim() !== '')
      };
      setEditableTextbooks(updatedBooks);
      setEditingTextbook(null);
      resetNewBook();
    }
  };

  const handleSaveReference = () => {
    if (editingReference !== null) {
      const updatedBooks = [...editableReferences];
      updatedBooks[editingReference] = {
        ...newBook,
        authors: newBook.authors.filter(author => author.trim() !== '')
      };
      setEditableReferences(updatedBooks);
      setEditingReference(null);
      resetNewBook();
    }
  };

  const handleCancelEdit = () => {
    setEditingTextbook(null);
    setEditingReference(null);
    resetNewBook();
  };

  const resetNewBook = () => {
    setNewBook({
      title: '',
      authors: [''],
      publisher: '',
      year: new Date().getFullYear(),
      isbn: '',
      edition: ''
    });
  };

  const resetNewOutcome = () => {
    setNewOutcome({
      id: '',
      description: '',
      bloomsLevel: 'Remember'
    });
  };

  const handleOutcomeEdit = (outcomeId: string) => {
    const outcome = editableOutcomes.find(o => o.id === outcomeId);
    if (outcome) {
      setNewOutcome(outcome);
      setEditingOutcome(outcomeId);
    }
  };

  const handleOutcomeSave = () => {
    if (editingOutcome && newOutcome.description.trim()) {
      const updatedOutcomes = editableOutcomes.map(outcome =>
        outcome.id === editingOutcome ? { ...newOutcome } : outcome
      );
      setEditableOutcomes(updatedOutcomes);
      setEditingOutcome(null);
      resetNewOutcome();
    }
  };

  const handleOutcomeAdd = () => {
    if (newOutcome.description.trim()) {
      const newId = `CO${editableOutcomes.length + 1}`;
      const outcomeToAdd = { ...newOutcome, id: newId };
      setEditableOutcomes([...editableOutcomes, outcomeToAdd]);
      resetNewOutcome();
    }
  };

  const handleOutcomeDelete = (outcomeId: string) => {
    const updatedOutcomes = editableOutcomes.filter(outcome => outcome.id !== outcomeId);
    // Renumber the remaining outcomes
    const renumberedOutcomes = updatedOutcomes.map((outcome, index) => ({
      ...outcome,
      id: `CO${index + 1}`
    }));
    setEditableOutcomes(renumberedOutcomes);
  };

  const handleAuthorChange = (index: number, value: string) => {
    const updatedAuthors = [...newBook.authors];
    updatedAuthors[index] = value;
    setNewBook({ ...newBook, authors: updatedAuthors });
  };

  const addAuthor = () => {
    setNewBook({ ...newBook, authors: [...newBook.authors, ''] });
  };

  const removeAuthor = (index: number) => {
    if (newBook.authors.length > 1) {
      const updatedAuthors = newBook.authors.filter((_, i) => i !== index);
      setNewBook({ ...newBook, authors: updatedAuthors });
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8" id="course-content">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-600 via-blue-600 to-purple-600 text-white rounded-2xl p-8 shadow-2xl border border-cyan-500/20">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-cyan-100 bg-clip-text text-transparent">
              {course.title}
            </h1>
            <div className="flex items-center gap-6 text-cyan-100">
              <span className="flex items-center gap-2 bg-white/10 px-3 py-1 rounded-full">
                <FileText className="w-4 h-4" />
                {course.code}
              </span>
              <span className="flex items-center gap-2 bg-white/10 px-3 py-1 rounded-full">
                <Award className="w-4 h-4" />
                {course.credits} Credits
              </span>
              <span className="flex items-center gap-2 bg-white/10 px-3 py-1 rounded-full">
                <Clock className="w-4 h-4" />
                {totalContactHours} Hours
              </span>
            </div>
          </div>
          <div className="flex gap-3">
            <button
              onClick={onExportPDF}
              className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 backdrop-blur-sm transform hover:scale-105"
            >
              <Download className="w-4 h-4" />
              Export PDF
            </button>
            <button
              onClick={onStartNew}
              className="bg-white text-blue-600 hover:bg-cyan-50 px-6 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 transform hover:scale-105"
            >
              <Sparkles className="w-4 h-4" />
              New Course
            </button>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mt-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-sm text-cyan-100 mb-2 font-medium">Contact Hours</div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Lecture:</span>
                <span className="font-semibold">{course.contactHours.lecture}h</span>
              </div>
              <div className="flex justify-between">
                <span>Tutorial:</span>
                <span className="font-semibold">{course.contactHours.tutorial}h</span>
              </div>
              <div className="flex justify-between">
                <span>Practical:</span>
                <span className="font-semibold">{course.contactHours.practical}h</span>
              </div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-sm text-cyan-100 mb-2 font-medium">Assessment Pattern</div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Internal:</span>
                <span className="font-semibold">{course.assessmentPattern.internalAssessment}%</span>
              </div>
              <div className="flex justify-between">
                <span>End Sem:</span>
                <span className="font-semibold">{course.assessmentPattern.endSemExam}%</span>
              </div>
              {course.assessmentPattern.practical && (
                <div className="flex justify-between">
                  <span>Practical:</span>
                  <span className="font-semibold">{course.assessmentPattern.practical}%</span>
                </div>
              )}
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-sm text-cyan-100 mb-2 font-medium">Generated</div>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Calendar className="w-3 h-3" />
                <span className="text-sm">{course.createdAt.toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <GraduationCap className="w-3 h-3" />
                <span className="text-sm">{course.generatedBy}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Prerequisites */}
      {course.prerequisites.length > 0 && (
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-2 rounded-xl">
              <Users className="w-6 h-6 text-white" />
            </div>
            Prerequisites
          </h3>
          <div className="flex flex-wrap gap-3">
            {course.prerequisites.map((prereq, index) => (
              <span
                key={index}
                className="bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-indigo-300 px-4 py-2 rounded-full text-sm font-medium border border-indigo-500/30 backdrop-blur-sm"
              >
                {prereq}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Course Context & Industry Relevance */}
      <div className="grid md:grid-cols-2 gap-8">
        {/* Industry Relevance */}
        {course.industryRelevance && (
          <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
            <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
              <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-2 rounded-xl">
                <Briefcase className="w-6 h-6 text-white" />
              </div>
              Industry Relevance
            </h3>

            <div className="space-y-6">
              {/* Skills */}
              <div>
                <h4 className="font-semibold text-emerald-300 mb-3 flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Key Skills
                </h4>
                <div className="flex flex-wrap gap-2">
                  {course.industryRelevance.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="bg-emerald-500/20 text-emerald-300 px-3 py-1 rounded-lg text-sm border border-emerald-500/30"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Tools */}
              <div>
                <h4 className="font-semibold text-teal-300 mb-3 flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  Essential Tools
                </h4>
                <div className="flex flex-wrap gap-2">
                  {course.industryRelevance.tools.map((tool, index) => (
                    <span
                      key={index}
                      className="bg-teal-500/20 text-teal-300 px-3 py-1 rounded-lg text-sm border border-teal-500/30"
                    >
                      {tool}
                    </span>
                  ))}
                </div>
              </div>

              {/* Career Opportunities */}
              <div>
                <h4 className="font-semibold text-cyan-300 mb-3 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Career Opportunities
                </h4>
                <div className="flex flex-wrap gap-2">
                  {course.industryRelevance.careerOpportunities.map((career, index) => (
                    <span
                      key={index}
                      className="bg-cyan-500/20 text-cyan-300 px-3 py-1 rounded-lg text-sm border border-cyan-500/30"
                    >
                      {career}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}


      </div>

      {/* Course Objectives */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
          <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-2 rounded-xl">
            <Target className="w-6 h-6 text-white" />
          </div>
          Course Objectives
        </h3>
        <div className="space-y-4">
          {course.objectives.map((objective) => (
            <div key={objective.id} className="flex gap-4 p-4 bg-gray-900/30 rounded-xl border border-gray-700/30">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                {objective.id}
              </div>
              <p className="text-gray-300 leading-relaxed">{objective.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Course Outcomes */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
          <div className="bg-gradient-to-r from-purple-500 to-violet-500 p-2 rounded-xl">
            <Award className="w-6 h-6 text-white" />
          </div>
          Course Outcomes
          <button
            onClick={handleOutcomeAdd}
            className="ml-auto bg-purple-500 hover:bg-purple-600 text-white p-2 rounded-lg transition-colors"
            title="Add New Outcome"
          >
            <Plus className="w-4 h-4" />
          </button>
        </h3>

        <div className="grid gap-4">
          {editableOutcomes.map((outcome) => (
            <div key={outcome.id} className="border border-purple-500/30 rounded-xl p-6 hover:bg-purple-500/5 transition-all duration-300 bg-gray-900/20 group">
              {editingOutcome === outcome.id ? (
                <div className="space-y-4">
                  <div className="flex gap-4">
                    <input
                      type="text"
                      value={newOutcome.id}
                      onChange={(e) => setNewOutcome(prev => ({ ...prev, id: e.target.value }))}
                      className="w-20 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
                      placeholder="CO1"
                    />
                    <select
                      value={newOutcome.bloomsLevel}
                      onChange={(e) => setNewOutcome(prev => ({ ...prev, bloomsLevel: e.target.value }))}
                      className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
                    >
                      <option value="Remember">Remember</option>
                      <option value="Understand">Understand</option>
                      <option value="Apply">Apply</option>
                      <option value="Analyze">Analyze</option>
                      <option value="Evaluate">Evaluate</option>
                      <option value="Create">Create</option>
                    </select>
                  </div>
                  <textarea
                    value={newOutcome.description}
                    onChange={(e) => setNewOutcome(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white resize-none"
                    rows={3}
                    placeholder="Enter outcome description..."
                  />
                  <div className="flex gap-2">
                    <button
                      onClick={handleOutcomeSave}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
                    >
                      <Save className="w-4 h-4" />
                      Save
                    </button>
                    <button
                      onClick={() => {
                        setEditingOutcome(null);
                        resetNewOutcome();
                      }}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-start mb-3">
                    <span className="bg-gradient-to-r from-purple-500 to-violet-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                      {outcome.id}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="bg-gray-700/50 text-gray-300 px-3 py-1 rounded-full text-xs font-medium">
                        {outcome.bloomsLevel}
                      </span>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                        <button
                          onClick={() => handleOutcomeEdit(outcome.id)}
                          className="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded transition-colors"
                          title="Edit Outcome"
                        >
                          <Edit3 className="w-3 h-3" />
                        </button>
                        <button
                          onClick={() => handleOutcomeDelete(outcome.id)}
                          className="bg-red-500 hover:bg-red-600 text-white p-1 rounded transition-colors"
                          title="Delete Outcome"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-300 leading-relaxed">{outcome.description}</p>
                </>
              )}
            </div>
          ))}

          {/* Add New Outcome Form */}
          {editingOutcome === null && (
            <div className="border border-dashed border-purple-500/50 rounded-xl p-6 bg-gray-900/10">
              <div className="space-y-4">
                <div className="flex gap-4">
                  <input
                    type="text"
                    value={newOutcome.id || `CO${editableOutcomes.length + 1}`}
                    onChange={(e) => setNewOutcome(prev => ({ ...prev, id: e.target.value }))}
                    className="w-20 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
                    placeholder={`CO${editableOutcomes.length + 1}`}
                  />
                  <select
                    value={newOutcome.bloomsLevel}
                    onChange={(e) => setNewOutcome(prev => ({ ...prev, bloomsLevel: e.target.value }))}
                    className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm"
                  >
                    <option value="Remember">Remember</option>
                    <option value="Understand">Understand</option>
                    <option value="Apply">Apply</option>
                    <option value="Analyze">Analyze</option>
                    <option value="Evaluate">Evaluate</option>
                    <option value="Create">Create</option>
                  </select>
                </div>
                <textarea
                  value={newOutcome.description}
                  onChange={(e) => setNewOutcome(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white resize-none"
                  rows={3}
                  placeholder="Enter new outcome description..."
                />
                <button
                  onClick={handleOutcomeAdd}
                  disabled={!newOutcome.description.trim()}
                  className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Outcome
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Course Units */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-8 flex items-center gap-3">
          <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-xl">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          Course Curriculum
        </h3>
        <div className="space-y-8">
          {course.units.map((unit) => (
            <div key={unit.unitNumber} className="border border-gray-700/50 rounded-2xl p-8 hover:shadow-xl transition-all duration-500 bg-gray-900/20 hover:bg-gray-900/30">
              <div className="flex justify-between items-start mb-6">
                <h4 className="text-xl font-bold text-cyan-300">
                  Unit {unit.unitNumber}: {unit.title}
                </h4>
                <span className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                  {unit.contactHours} hours
                </span>
              </div>
              
              <div className="space-y-8">
                <div>
                  <h5 className="font-semibold text-cyan-400 mb-4 flex items-center gap-2">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                    Topics Covered
                  </h5>
                  <div className="space-y-4">
                    {unit.topics.map((topic, index) => (
                      <div key={index} className="border-l-4 border-cyan-500/30 pl-4 py-2 bg-gray-900/20 rounded-r-lg">
                        <h6 className="text-cyan-300 font-medium mb-2">{topic}</h6>
                        {unit.topicDescriptions && unit.topicDescriptions[topic] && (
                          <p className="text-gray-400 text-sm leading-relaxed">
                            {unit.topicDescriptions[topic]}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-semibold text-purple-400 mb-4 flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      Learning Outcomes
                    </h5>
                    <ul className="space-y-2">
                      {unit.learningOutcomes.map((outcome, index) => (
                        <li key={index} className="text-gray-300 flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                          {outcome}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {unit.assignments && unit.assignments.length > 0 && (
                    <div>
                      <h5 className="font-semibold text-green-400 mb-4 flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        Assignments
                      </h5>
                      <ul className="space-y-2">
                        {unit.assignments.map((assignment, index) => (
                          <li key={index} className="text-gray-300 flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                            {assignment}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {(unit.practicalExercises || unit.caseStudies) && (
                  <div className="grid md:grid-cols-2 gap-6 mt-6 pt-6 border-t border-gray-700/30">
                    {unit.practicalExercises && unit.practicalExercises.length > 0 && (
                      <div>
                        <h5 className="font-semibold text-orange-400 mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                          Practical Exercises
                        </h5>
                        <ul className="space-y-2">
                          {unit.practicalExercises.map((exercise, index) => (
                            <li key={index} className="text-gray-300 flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                              {exercise}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {unit.caseStudies && unit.caseStudies.length > 0 && (
                      <div>
                        <h5 className="font-semibold text-pink-400 mb-4 flex items-center gap-2">
                          <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                          Case Studies
                        </h5>
                        <ul className="space-y-2">
                          {unit.caseStudies.map((caseStudy, index) => (
                            <li key={index} className="text-gray-300 flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-pink-400 rounded-full mt-2 flex-shrink-0"></div>
                              {caseStudy}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Resources Section */}
      <div className="grid md:grid-cols-2 gap-8">
        {/* Textbooks */}
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-orange-500 to-amber-500 p-2 rounded-xl">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            Prescribed Textbooks
          </h3>
          <div className="space-y-6">
            {editableTextbooks.map((book, index) => (
              <div key={index} className="relative border-l-4 border-orange-500 pl-6 py-2 bg-gray-900/20 rounded-r-xl group">
                {editingTextbook === index ? (
                  // Edit Mode
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-orange-300 mb-2">Title</label>
                      <input
                        type="text"
                        value={newBook.title}
                        onChange={(e) => setNewBook({ ...newBook, title: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        placeholder="Book title"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-orange-300 mb-2">Authors</label>
                      {newBook.authors.map((author, authorIndex) => (
                        <div key={authorIndex} className="flex gap-2 mb-2">
                          <input
                            type="text"
                            value={author}
                            onChange={(e) => handleAuthorChange(authorIndex, e.target.value)}
                            className="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            placeholder={`Author ${authorIndex + 1}`}
                          />
                          {newBook.authors.length > 1 && (
                            <button
                              onClick={() => removeAuthor(authorIndex)}
                              className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        onClick={addAuthor}
                        className="flex items-center gap-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
                      >
                        <Plus className="w-4 h-4" />
                        Add Author
                      </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-orange-300 mb-2">Publisher</label>
                        <input
                          type="text"
                          value={newBook.publisher}
                          onChange={(e) => setNewBook({ ...newBook, publisher: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="Publisher name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-orange-300 mb-2">Year</label>
                        <input
                          type="number"
                          value={newBook.year}
                          onChange={(e) => setNewBook({ ...newBook, year: parseInt(e.target.value) || new Date().getFullYear() })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="Publication year"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-orange-300 mb-2">ISBN (Optional)</label>
                        <input
                          type="text"
                          value={newBook.isbn}
                          onChange={(e) => setNewBook({ ...newBook, isbn: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="ISBN number"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-orange-300 mb-2">Edition (Optional)</label>
                        <input
                          type="text"
                          value={newBook.edition}
                          onChange={(e) => setNewBook({ ...newBook, edition: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          placeholder="Edition"
                        />
                      </div>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <button
                        onClick={handleSaveTextbook}
                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Save className="w-4 h-4" />
                        Save
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <X className="w-4 h-4" />
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  // Display Mode
                  <>
                    <button
                      onClick={() => handleEditTextbook(index)}
                      className="absolute top-2 right-2 p-1 bg-orange-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-orange-700"
                      title="Edit this textbook"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <h4 className="font-semibold text-orange-300 mb-2 pr-8">{book.title}</h4>
                    <p className="text-gray-400 text-sm mb-1">
                      {book.authors.join(', ')} • {book.publisher} ({book.year})
                    </p>
                    {(book.isbn || book.edition) && (
                      <p className="text-gray-500 text-xs">
                        {book.isbn && `ISBN: ${book.isbn}`}
                        {book.isbn && book.edition && ' • '}
                        {book.edition && `${book.edition} Edition`}
                      </p>
                    )}
                  </>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Reference Books */}
        <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
          <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
            <div className="bg-gradient-to-r from-teal-500 to-cyan-500 p-2 rounded-xl">
              <FileText className="w-6 h-6 text-white" />
            </div>
            Reference Books
          </h3>
          <div className="space-y-6">
            {editableReferences.map((book, index) => (
              <div key={index} className="relative border-l-4 border-teal-500 pl-6 py-2 bg-gray-900/20 rounded-r-xl group">
                {editingReference === index ? (
                  // Edit Mode
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-teal-300 mb-2">Title</label>
                      <input
                        type="text"
                        value={newBook.title}
                        onChange={(e) => setNewBook({ ...newBook, title: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        placeholder="Book title"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-teal-300 mb-2">Authors</label>
                      {newBook.authors.map((author, authorIndex) => (
                        <div key={authorIndex} className="flex gap-2 mb-2">
                          <input
                            type="text"
                            value={author}
                            onChange={(e) => handleAuthorChange(authorIndex, e.target.value)}
                            className="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                            placeholder={`Author ${authorIndex + 1}`}
                          />
                          {newBook.authors.length > 1 && (
                            <button
                              onClick={() => removeAuthor(authorIndex)}
                              className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        onClick={addAuthor}
                        className="flex items-center gap-2 px-3 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors text-sm"
                      >
                        <Plus className="w-4 h-4" />
                        Add Author
                      </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-teal-300 mb-2">Publisher</label>
                        <input
                          type="text"
                          value={newBook.publisher}
                          onChange={(e) => setNewBook({ ...newBook, publisher: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                          placeholder="Publisher name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-teal-300 mb-2">Year</label>
                        <input
                          type="number"
                          value={newBook.year}
                          onChange={(e) => setNewBook({ ...newBook, year: parseInt(e.target.value) || new Date().getFullYear() })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                          placeholder="Publication year"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-teal-300 mb-2">ISBN (Optional)</label>
                        <input
                          type="text"
                          value={newBook.isbn}
                          onChange={(e) => setNewBook({ ...newBook, isbn: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                          placeholder="ISBN number"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-teal-300 mb-2">Edition (Optional)</label>
                        <input
                          type="text"
                          value={newBook.edition}
                          onChange={(e) => setNewBook({ ...newBook, edition: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                          placeholder="Edition"
                        />
                      </div>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <button
                        onClick={handleSaveReference}
                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Save className="w-4 h-4" />
                        Save
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <X className="w-4 h-4" />
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  // Display Mode
                  <>
                    <button
                      onClick={() => handleEditReference(index)}
                      className="absolute top-2 right-2 p-1 bg-teal-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-teal-700"
                      title="Edit this reference book"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <h4 className="font-semibold text-teal-300 mb-2 pr-8">{book.title}</h4>
                    <p className="text-gray-400 text-sm mb-1">
                      {book.authors.join(', ')} • {book.publisher} ({book.year})
                    </p>
                    {(book.isbn || book.edition) && (
                      <p className="text-gray-500 text-xs">
                        {book.isbn && `ISBN: ${book.isbn}`}
                        {book.isbn && book.edition && ' • '}
                        {book.edition && `${book.edition} Edition`}
                      </p>
                    )}
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Online Resources */}
      <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-gray-700/50">
        <h3 className="text-2xl font-semibold text-cyan-300 mb-6 flex items-center gap-3">
          <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-xl">
            <ExternalLink className="w-6 h-6 text-white" />
          </div>
          Online Resources & Websites
        </h3>
        <div className="grid md:grid-cols-1 gap-4">
          {course.onlineResources.map((resource, index) => {
            // Parse resource string to extract URL and description
            const parts = resource.split(' - ');
            const url = parts[0].trim();
            const description = parts.length > 1 ? parts[1].trim() : resource;
            const isValidUrl = url.startsWith('http://') || url.startsWith('https://');

            return (
              <div key={index} className="flex items-center gap-3 p-4 border border-gray-700/50 rounded-xl hover:bg-gray-900/30 transition-all duration-300 bg-gray-900/20">
                <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-lg flex-shrink-0">
                  <ExternalLink className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  {isValidUrl ? (
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-cyan-400 hover:text-cyan-300 transition-colors font-medium block"
                    >
                      {description}
                    </a>
                  ) : (
                    <span className="text-gray-300">{resource}</span>
                  )}
                  {isValidUrl && (
                    <p className="text-xs text-gray-500 mt-1 truncate">{url}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>



      {/* Footer */}
      <div className="bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-xl rounded-2xl p-8 text-center border border-gray-700/50">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Sparkles className="w-6 h-6 text-cyan-400" />
          <span className="text-lg font-semibold text-cyan-300">AI-Generated Course Syllabus</span>
        </div>
        <p className="text-gray-400 leading-relaxed">
          This course syllabus was generated using AI-powered curriculum design system with quality validation.
          <br />
          Please review and adapt according to your institution's specific requirements and AICTE guidelines.
        </p>
      </div>
    </div>
  );
};