import React, { useState } from 'react';
import { CourseForm } from './components/CourseForm';
import { CourseDisplay } from './components/CourseDisplay';
import { AuthModal } from './components/AuthModal';
import { Course, CourseGenerationRequest } from './types/course';
import { MockLLMService as EnhancedMockLLMService } from './services/mockLLMService';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { exportToPDF } from './utils/exportPDF';
import databaseService from './services/databaseService';

function AppContent() {
  const { user, isAuthenticated } = useAuth();
  const [currentPage, setCurrentPage] = useState<'home' | 'form' | 'course'>('home');
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [mockService] = useState(new EnhancedMockLLMService());

  const handleStartGeneration = () => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }
    setCurrentPage('form');
  };

  const handleCourseGeneration = async (request: CourseGenerationRequest) => {
    if (!isAuthenticated || !user) {
      setShowAuthModal(true);
      return;
    }

    setIsLoading(true);
    try {
      const preferences = databaseService.getUserPreferences(user.id);
      const generatedCourse = await mockService.generateCourse(request, preferences);
      databaseService.saveCourse(user.id, generatedCourse);
      setCourse(generatedCourse);
      setCurrentPage('course');
    } catch (error) {
      console.error('Course generation failed:', error);
      alert('Failed to generate course. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportPDF = async () => {
    if (!course) return;
    try {
      const pdfElement = document.getElementById('course-content-pdf');
      const originalElement = document.getElementById('course-content');
      
      if (pdfElement && originalElement) {
        pdfElement.classList.remove('hidden');
        originalElement.style.display = 'none';
        await exportToPDF('course-content-pdf', `CourseCraft_${course.code}_${course.title.replace(/\s+/g, '_')}.pdf`);
        pdfElement.classList.add('hidden');
        originalElement.style.display = 'block';
      } else {
        await exportToPDF('course-content', `CourseCraft_${course.code}_${course.title.replace(/\s+/g, '_')}.pdf`);
      }
    } catch (error) {
      console.error('PDF Export Error:', error);
      alert('Failed to export PDF. Please try again.');
    }
  };

  const handleStartNew = () => {
    setCourse(null);
    setCurrentPage('home');
  };

  if (currentPage === 'course' && course) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#111827', padding: '20px' }}>
        <CourseDisplay
          course={course}
          onExportPDF={handleExportPDF}
          onStartNew={handleStartNew}
        />
      </div>
    );
  }

  if (currentPage === 'form') {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#111827', padding: '20px' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <CourseForm onSubmit={handleCourseGeneration} isLoading={isLoading} />
        </div>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#111827', color: 'white', padding: '20px' }}>
      <h1 style={{ fontSize: '48px', textAlign: 'center', marginBottom: '20px' }}>CourseCraft</h1>
      <p style={{ fontSize: '18px', textAlign: 'center', marginBottom: '40px' }}>AI-Powered Curriculum Generator</p>
      
      <div style={{ textAlign: 'center' }}>
        <button 
          onClick={handleStartGeneration}
          style={{ 
            backgroundColor: '#06B6D4', 
            color: 'white', 
            padding: '15px 30px', 
            fontSize: '18px', 
            border: 'none', 
            borderRadius: '8px', 
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Start Course Generation
        </button>
        
        <p style={{ marginTop: '20px', fontSize: '14px' }}>
          Auth Status: {isAuthenticated ? 'Logged In' : 'Not Logged In'}
        </p>
        <p style={{ fontSize: '14px' }}>
          User: {user ? user.fullName : 'None'}
        </p>
      </div>

      <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
