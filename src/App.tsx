import React, { useState, useEffect } from 'react';
import { Book<PERSON>pen, Sparkles, Target, Users, Award, MessageCircle, User, LogIn } from 'lucide-react';
import { CourseForm } from './components/CourseForm';
import { CourseDisplay } from './components/CourseDisplay';
import { ApiKeyDialog } from './components/ApiKeyDialog';
import { ChatBot } from './components/ChatBot';
import { LangChainDemo } from './components/LangChainDemo';
import { AuthModal } from './components/AuthModal';
import { UserProfile } from './components/UserProfile';
import { Course, CourseGenerationRequest } from './types/course';
import { LLMService, MockLLMService, LLMProvider } from './services/llmService';
import { MockLLMService as EnhancedMockLLMService } from './services/mockLLMService';
import { LangChainCourseService } from './services/langchainCourseService';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { exportToPDF } from './utils/exportPDF';
import databaseService from './services/databaseService';

function AppContent() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [currentPage, setCurrentPage] = useState<'home' | 'form' | 'course'>('home');
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showApiDialog, setShowApiDialog] = useState(false);
  const [showChatBot, setShowChatBot] = useState(false);
  const [showLangChainDemo, setShowLangChainDemo] = useState(false);

  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [apiKey, setApiKey] = useState<string>('');
  const [llmService, setLlmService] = useState<LLMService | MockLLMService | null>(null);
  const [mockService] = useState(new EnhancedMockLLMService());
  const [langchainService, setLangchainService] = useState<LangChainCourseService | null>(null);

  useEffect(() => {
    // Check for stored API key and provider
    const storedApiKey = localStorage.getItem('openai_api_key');
    const storedProvider = localStorage.getItem('llm_provider') as LLMProvider || 'openai';

    if (storedApiKey && storedApiKey !== 'demo') {
      setApiKey(storedApiKey);
      setLlmService(new LLMService({
        provider: storedProvider,
        apiKey: storedApiKey
      }));

      // Initialize LangChain service
      try {
        setLangchainService(new LangChainCourseService({
          provider: storedProvider,
          apiKey: storedApiKey,
          temperature: 0.7,
          maxTokens: 4000
        }));
      } catch (error) {
        console.error('Failed to initialize LangChain service:', error);
        setLangchainService(null);
      }
    } else if (storedApiKey === 'demo') {
      setApiKey('demo');
      setLlmService(new MockLLMService());
      setLangchainService(null);
    }
  }, []);

  const handleStartGeneration = () => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }

    // Use the enhanced mock service by default (no API key required)
    setCurrentPage('form');
  };

  const handleApiKeySave = (key: string, provider: LLMProvider = 'openai') => {
    setApiKey(key);
    localStorage.setItem('openai_api_key', key);
    localStorage.setItem('llm_provider', provider);

    if (key === 'demo') {
      setLlmService(new MockLLMService());
      setLangchainService(null);
    } else {
      setLlmService(new LLMService({
        provider,
        apiKey: key
      }));

      // Initialize LangChain service for advanced course generation
      try {
        setLangchainService(new LangChainCourseService({
          provider,
          apiKey: key,
          temperature: 0.7,
          maxTokens: 4000
        }));
      } catch (error) {
        console.error('Failed to initialize LangChain service:', error);
        setLangchainService(null);
      }
    }

    setCurrentPage('form');
  };

  const handleCourseGeneration = async (request: CourseGenerationRequest) => {
    if (!isAuthenticated || !user) {
      setShowAuthModal(true);
      return;
    }

    setIsLoading(true);
    try {
      // Get user preferences for CO and unit counts
      const preferences = databaseService.getUserPreferences(user.id);

      let generatedCourse: Course;

      // Use LangChain service if available (when API key is provided)
      if (langchainService && apiKey !== 'demo') {
        console.log('🚀 Using LangChain-powered course generation...');
        generatedCourse = await langchainService.generateCourse(request, preferences);
      } else {
        console.log('📚 Using enhanced mock service...');
        // Fallback to mock service
        generatedCourse = await mockService.generateCourse(request, preferences);
      }

      // Save course to database
      databaseService.saveCourse(user.id, generatedCourse);

      setCourse(generatedCourse);
      setCurrentPage('course');
    } catch (error) {
      console.error('Course generation failed:', error);

      // If LangChain fails, fallback to mock service
      if (langchainService && apiKey !== 'demo') {
        console.log('⚠️ LangChain failed, falling back to mock service...');
        try {
          const preferences = databaseService.getUserPreferences(user.id);
          const generatedCourse = await mockService.generateCourse(request, preferences);
          databaseService.saveCourse(user.id, generatedCourse);
          setCourse(generatedCourse);
          setCurrentPage('course');
        } catch (fallbackError) {
          console.error('Fallback also failed:', fallbackError);
          alert('Failed to generate course. Please try again.');
        }
      } else {
        alert('Failed to generate course. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportPDF = async () => {
    if (!course) return;

    try {
      await exportToPDF(course, `${course.code}_${course.title.replace(/\s+/g, '_')}.pdf`);
    } catch (error) {
      alert('Failed to export PDF. Please try again.');
    }
  };

  const handleStartNew = () => {
    setCourse(null);
    setCurrentPage('form');
  };

  const handleBackToHome = () => {
    setCourse(null);
    setCurrentPage('home');
  };

  if (currentPage === 'course' && course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 py-8 px-4">
        <CourseDisplay
          course={course}
          onExportPDF={handleExportPDF}
          onStartNew={handleStartNew}
        />
        
        {/* Floating ChatBot Button */}
        <button
          onClick={() => setShowChatBot(true)}
          className="fixed bottom-6 right-6 bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-4 rounded-full shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-110 z-50 animate-pulse"
        >
          <MessageCircle className="w-6 h-6" />
        </button>

        <ChatBot isOpen={showChatBot} onClose={() => setShowChatBot(false)} />
      </div>
    );
  }

  if (currentPage === 'form') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <button
            onClick={handleBackToHome}
            className="mb-6 text-cyan-400 hover:text-cyan-300 flex items-center gap-2 transition-all duration-300 transform hover:translate-x-2"
          >
            ← Back to Home
          </button>
          <CourseForm onSubmit={handleCourseGeneration} isLoading={isLoading} />
        </div>
        
        {/* Floating ChatBot Button */}
        <button
          onClick={() => setShowChatBot(true)}
          className="fixed bottom-6 right-6 bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-4 rounded-full shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-110 z-50 animate-pulse"
        >
          <MessageCircle className="w-6 h-6" />
        </button>

        <ChatBot isOpen={showChatBot} onClose={() => setShowChatBot(false)} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="bg-gray-900/80 backdrop-blur-xl border-b border-gray-700/50 sticky top-0 z-40 shadow-2xl">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 p-1 rounded-2xl shadow-2xl transform hover:scale-110 transition-all duration-300 hover:shadow-cyan-500/50 animate-logo-glow">
                  <div className="bg-gray-900 p-2 rounded-xl">
                    <img
                      src="/logomini.jpeg"
                      alt="CourseCraft Logo"
                      className="w-10 h-10 rounded-xl object-cover"
                    />
                  </div>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 bg-clip-text text-transparent tracking-wide animate-brand-pulse">
                  CourseCraft
                </h1>
                <p className="text-sm font-medium bg-gradient-to-r from-gray-300 to-gray-400 bg-clip-text text-transparent">
                  AI-Powered Curriculum Generator
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {isAuthenticated && user ? (
                <div className="flex items-center gap-3">
                  <div className="text-sm text-gray-400 bg-gray-800/50 px-3 py-1 rounded-full">
                    Welcome, {user.fullName.split(' ')[0]}!
                  </div>
                  <button
                    onClick={() => setShowUserProfile(true)}
                    className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-2 rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 flex items-center gap-2"
                  >
                    <User className="w-4 h-4" />
                    Profile
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setShowUserProfile(true)}
                    className="bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 border border-gray-600/50"
                  >
                    <User className="w-4 h-4" />
                    Profile
                  </button>
                  <button
                    onClick={() => setShowAuthModal(true)}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 flex items-center gap-2"
                  >
                    <LogIn className="w-4 h-4" />
                    Sign In
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-cyan-500/30 to-purple-500/30 backdrop-blur-xl text-white px-8 py-4 rounded-2xl text-lg font-bold mb-8 border border-cyan-400/50 shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300">
              <div className="relative">
                <div className="bg-gradient-to-r from-cyan-400 to-purple-400 p-1 rounded-xl animate-logo-glow">
                  <img
                    src="/logomini.jpeg"
                    alt="CourseCraft"
                    className="w-8 h-8 rounded-lg object-cover"
                  />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <span className="bg-gradient-to-r from-cyan-200 to-purple-200 bg-clip-text text-transparent font-black text-xl animate-brand-pulse">
                CourseCraft
              </span>
              <span className="text-cyan-200">•</span>
              <span className="text-cyan-300 font-medium">AI-Powered Course Design</span>
            </div>
            <h1 className="text-7xl font-black mb-8 leading-tight">
              <span className="bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 bg-clip-text text-transparent drop-shadow-2xl">
                Design Complete University
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-300 via-pink-300 to-red-300 bg-clip-text text-transparent drop-shadow-2xl">
                Course Syllabi
              </span>
              <br />
              <span className="text-5xl bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent font-black mt-6 block animate-brand-pulse">
                with CourseCraft AI
              </span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
              Generate comprehensive, AICTE-compliant course syllabi for Indian universities using 
              advanced AI. Create complete curricula with objectives, outcomes, units, and resources in minutes.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl p-8 border border-gray-700/50 shadow-2xl hover:shadow-cyan-500/10 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-cyan-300 mb-4">Complete Objectives</h3>
              <p className="text-gray-400 leading-relaxed">Auto-generate clear learning objectives and measurable outcomes aligned with Bloom's taxonomy.</p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl p-8 border border-gray-700/50 shadow-2xl hover:shadow-purple-500/10 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
              <div className="bg-gradient-to-r from-purple-500 to-violet-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-purple-300 mb-4">Structured Curriculum</h3>
              <p className="text-gray-400 leading-relaxed">5 comprehensive units covering subject fundamentals with proper contact hour distribution.</p>
            </div>

            <div className="bg-gray-800/40 backdrop-blur-xl rounded-2xl p-8 border border-gray-700/50 shadow-2xl hover:shadow-orange-500/10 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-orange-300 mb-4">AICTE Compliant</h3>
              <p className="text-gray-400 leading-relaxed">Follows Indian university standards with proper assessment patterns and resource recommendations.</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
            <button
              onClick={handleStartGeneration}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold px-12 py-6 rounded-2xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-cyan-500/25 text-xl transform hover:scale-105 hover:-translate-y-1"
            >
              Start Course Generation
            </button>



          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 bg-gray-800/20 backdrop-blur-sm relative z-10">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-center mb-12">
            <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
              Everything You Need for Course Design
            </span>
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { icon: Target, title: 'Learning Objectives', desc: 'Clear, measurable course objectives', color: 'from-green-500 to-emerald-500' },
              { icon: Award, title: 'Course Outcomes', desc: 'Bloom\'s taxonomy aligned outcomes', color: 'from-purple-500 to-violet-500' },
              { icon: BookOpen, title: 'Curriculum Units', desc: '5 structured units with detailed topics', color: 'from-blue-500 to-cyan-500' },
              { icon: Users, title: 'Contact Hours', desc: 'Proper lecture/tutorial/lab distribution', color: 'from-pink-500 to-rose-500' },
              { icon: BookOpen, title: 'Textbooks', desc: 'Curated textbook recommendations', color: 'from-orange-500 to-amber-500' },
              { icon: BookOpen, title: 'References', desc: 'Additional reference materials', color: 'from-teal-500 to-cyan-500' },
              { icon: Target, title: 'Assessment', desc: 'Indian university assessment patterns', color: 'from-indigo-500 to-purple-500' },
              { icon: Award, title: 'Export Ready', desc: 'PDF export for official use', color: 'from-red-500 to-pink-500' }
            ].map((feature, index) => (
              <div key={index} className="bg-gray-800/40 backdrop-blur-xl rounded-2xl p-6 shadow-2xl hover:shadow-lg transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 border border-gray-700/50">
                <div className={`bg-gradient-to-r ${feature.color} w-12 h-12 rounded-xl flex items-center justify-center mb-4 shadow-lg`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-cyan-300 mb-2">{feature.title}</h3>
                <p className="text-gray-400 text-sm leading-relaxed">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900/80 backdrop-blur-xl text-white py-12 px-4 border-t border-gray-700/50 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="relative">
              <div className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 p-1 rounded-2xl shadow-xl">
                <div className="bg-gray-900 p-2 rounded-xl">
                  <img
                    src="/logomini.jpeg"
                    alt="CourseCraft Logo"
                    className="w-8 h-8 rounded-xl object-cover"
                  />
                </div>
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <div className="text-center">
              <span className="text-2xl font-bold bg-gradient-to-r from-cyan-300 to-purple-300 bg-clip-text text-transparent tracking-wide animate-brand-pulse">
                CourseCraft
              </span>
              <p className="text-sm text-gray-400 font-medium">AI-Powered Education Technology</p>
            </div>
          </div>
          <p className="text-gray-400 mb-6">
            Empowering Indian universities with AI-powered curriculum design
          </p>
          <div className="border-t border-gray-700 pt-6">
            <p className="text-sm text-gray-500">
              Built for AICTE compliance and Indian university standards
            </p>
          </div>
        </div>
      </footer>

      {/* Floating ChatBot Button */}
      <button
        onClick={() => setShowChatBot(true)}
        className="fixed bottom-6 right-6 bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-4 rounded-full shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-110 z-50 animate-pulse"
      >
        <MessageCircle className="w-6 h-6" />
      </button>

      <ApiKeyDialog
        isOpen={showApiDialog}
        onClose={() => setShowApiDialog(false)}
        onSave={handleApiKeySave}
      />

      <ChatBot isOpen={showChatBot} onClose={() => setShowChatBot(false)} />
      <LangChainDemo isOpen={showLangChainDemo} onClose={() => setShowLangChainDemo(false)} />
      <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
      <UserProfile
        isOpen={showUserProfile}
        onClose={() => setShowUserProfile(false)}
        onShowAuth={() => {
          setShowUserProfile(false);
          setShowAuthModal(true);
        }}
      />
    </div>
  );
}

function App() {
  try {
    return (
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    );
  } catch (error) {
    console.error('App error:', error);
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 text-red-400">Error Loading App</h1>
          <p className="text-xl text-gray-300">Please check the console for details</p>
          <pre className="mt-4 text-sm text-gray-400 bg-gray-800 p-4 rounded">
            {error?.toString()}
          </pre>
        </div>
      </div>
    );
  }
}

export default App;