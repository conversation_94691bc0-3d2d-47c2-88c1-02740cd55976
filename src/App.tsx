import React, { useState } from 'react';
import { BookOpen, Sparkles, Target, Users, Award, MessageCircle, User, LogIn } from 'lucide-react';
import { CourseForm } from './components/CourseForm';
import { CourseDisplay } from './components/CourseDisplay';
import { AuthModal } from './components/AuthModal';
import { ChatBot } from './components/ChatBot';
import { LangChainDemo } from './components/LangChainDemo';
import { UserProfile } from './components/UserProfile';
import { Course, CourseGenerationRequest } from './types/course';
import { MockLLMService as EnhancedMockLLMService } from './services/mockLLMService';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { exportToPDF } from './utils/exportPDF';
import databaseService from './services/databaseService';

function AppContent() {
  const { user, isAuthenticated } = useAuth();
  const [currentPage, setCurrentPage] = useState<'home' | 'form' | 'course'>('home');
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showChatBot, setShowChatBot] = useState(false);
  const [showLangChainDemo, setShowLangChainDemo] = useState(false);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [mockService] = useState(new EnhancedMockLLMService());

  const handleStartGeneration = () => {
    if (!isAuthenticated) {
      setShowAuthModal(true);
      return;
    }
    setCurrentPage('form');
  };

  const handleCourseGeneration = async (request: CourseGenerationRequest) => {
    if (!isAuthenticated || !user) {
      setShowAuthModal(true);
      return;
    }

    setIsLoading(true);
    try {
      const preferences = databaseService.getUserPreferences(user.id);
      const generatedCourse = await mockService.generateCourse(request, preferences);
      databaseService.saveCourse(user.id, generatedCourse);
      setCourse(generatedCourse);
      setCurrentPage('course');
    } catch (error) {
      console.error('Course generation failed:', error);
      alert('Failed to generate course. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportPDF = async () => {
    if (!course) return;
    try {
      const pdfElement = document.getElementById('course-content-pdf');
      const originalElement = document.getElementById('course-content');
      
      if (pdfElement && originalElement) {
        pdfElement.classList.remove('hidden');
        originalElement.style.display = 'none';
        await exportToPDF('course-content-pdf', `CourseCraft_${course.code}_${course.title.replace(/\s+/g, '_')}.pdf`);
        pdfElement.classList.add('hidden');
        originalElement.style.display = 'block';
      } else {
        await exportToPDF('course-content', `CourseCraft_${course.code}_${course.title.replace(/\s+/g, '_')}.pdf`);
      }
    } catch (error) {
      console.error('PDF Export Error:', error);
      alert('Failed to export PDF. Please try again.');
    }
  };

  const handleStartNew = () => {
    setCourse(null);
    setCurrentPage('home');
  };

  if (currentPage === 'course' && course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 py-8 px-4">
        <CourseDisplay
          course={course}
          onExportPDF={handleExportPDF}
          onStartNew={handleStartNew}
        />

        {/* Floating ChatBot Button */}
        <button
          onClick={() => setShowChatBot(true)}
          className="fixed bottom-6 right-6 bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-4 rounded-full shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-110 z-50 animate-pulse"
        >
          <MessageCircle className="w-6 h-6" />
        </button>

        <ChatBot isOpen={showChatBot} onClose={() => setShowChatBot(false)} />
      </div>
    );
  }

  if (currentPage === 'form') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <CourseForm onSubmit={handleCourseGeneration} isLoading={isLoading} />
        </div>

        {/* Floating ChatBot Button */}
        <button
          onClick={() => setShowChatBot(true)}
          className="fixed bottom-6 right-6 bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-4 rounded-full shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-110 z-50 animate-pulse"
        >
          <MessageCircle className="w-6 h-6" />
        </button>

        <ChatBot isOpen={showChatBot} onClose={() => setShowChatBot(false)} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="bg-gray-900/80 backdrop-blur-xl border-b border-gray-700/50 sticky top-0 z-40 shadow-2xl">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 p-1 rounded-2xl shadow-2xl transform hover:scale-110 transition-all duration-300 hover:shadow-cyan-500/50 animate-logo-glow">
                  <div className="bg-gray-900 p-2 rounded-xl">
                    <img
                      src="/logomini.jpeg"
                      alt="CourseCraft Logo"
                      className="w-10 h-10 rounded-xl object-cover"
                    />
                  </div>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 bg-clip-text text-transparent tracking-wide animate-brand-pulse">
                  CourseCraft
                </h1>
                <p className="text-sm font-medium bg-gradient-to-r from-gray-300 to-gray-400 bg-clip-text text-transparent">
                  AI-Powered Curriculum Generator
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {isAuthenticated && user ? (
                <>
                  <button
                    onClick={() => setShowUserProfile(true)}
                    className="flex items-center gap-2 bg-gray-800/50 hover:bg-gray-700/50 px-4 py-2 rounded-xl transition-all duration-300 border border-gray-600/50"
                  >
                    <User className="w-4 h-4 text-cyan-400" />
                    <span className="text-sm text-gray-300">{user.fullName}</span>
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setShowAuthModal(true)}
                  className="flex items-center gap-2 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 px-4 py-2 rounded-xl transition-all duration-300 text-white font-medium"
                >
                  <LogIn className="w-4 h-4" />
                  Login
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex items-center justify-center min-h-[calc(100vh-80px)] px-4">
        <div className="text-center max-w-4xl mx-auto">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-cyan-500/30 to-purple-500/30 backdrop-blur-xl text-white px-8 py-4 rounded-2xl text-lg font-bold mb-8 border border-cyan-400/50 shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300">
            <div className="relative">
              <div className="bg-gradient-to-r from-cyan-400 to-purple-400 p-1 rounded-xl animate-logo-glow">
                <img
                  src="/logomini.jpeg"
                  alt="CourseCraft"
                  className="w-8 h-8 rounded-lg object-cover"
                />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <span className="bg-gradient-to-r from-cyan-200 to-purple-200 bg-clip-text text-transparent font-black text-xl animate-brand-pulse">
              CourseCraft
            </span>
            <span className="text-cyan-200">•</span>
            <span className="text-cyan-300 font-medium">AI-Powered Course Design</span>
          </div>

          <h1 className="text-7xl font-black mb-8 leading-tight">
            <span className="bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 bg-clip-text text-transparent drop-shadow-2xl">
              Design Complete University
            </span>
            <br />
            <span className="bg-gradient-to-r from-purple-300 via-pink-300 to-red-300 bg-clip-text text-transparent drop-shadow-2xl">
              Course Syllabi
            </span>
            <br />
            <span className="text-5xl bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent font-black mt-6 block animate-brand-pulse">
              with CourseCraft AI
            </span>
          </h1>

          <p className="text-xl text-gray-300 mb-12 leading-relaxed max-w-3xl mx-auto">
            Generate comprehensive, AICTE-compliant course syllabi with AI-powered curriculum design.
            Create detailed course outcomes, assessment patterns, and complete academic documentation in minutes.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
            <button
              onClick={handleStartGeneration}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold px-12 py-6 rounded-2xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-cyan-500/25 text-xl transform hover:scale-105 hover:-translate-y-1"
            >
              Start Course Generation
            </button>
          </div>
        </div>
      </main>

      {/* Modals */}
      <AuthModal isOpen={showAuthModal} onClose={() => setShowAuthModal(false)} />
      <ChatBot isOpen={showChatBot} onClose={() => setShowChatBot(false)} />
      <LangChainDemo isOpen={showLangChainDemo} onClose={() => setShowLangChainDemo(false)} />
      <UserProfile isOpen={showUserProfile} onClose={() => setShowUserProfile(false)} />
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
