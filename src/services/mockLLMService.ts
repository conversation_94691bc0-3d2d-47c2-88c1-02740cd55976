import { Course, CourseGenerationRequest } from '../types/course';

export class MockLLMService {
  private generateRandomId(): string {
    return `course_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCourseCode(department: string, semester: number): string {
    const deptCode = department.substring(0, 2).toUpperCase();
    const baseCode = 300 + semester;
    return `${deptCode}${baseCode}`;
  }

  private generateCourseOutcomes(subject: string, count: number = 5) {
    const bloomsLevels = ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'];
    const outcomes = [];

    for (let i = 1; i <= count; i++) {
      const bloomLevel = bloomsLevels[Math.min(i - 1, bloomsLevels.length - 1)];
      outcomes.push({
        id: `CO${i}`,
        description: `Students will be able to ${this.getActionVerb(bloomLevel)} key concepts and principles of ${subject}`,
        bloomsLevel: bloomLevel,
        poMapping: [`PO${i}`, `PO${Math.min(i + 1, 12)}`]
      });
    }

    return outcomes;
  }

  private getActionVerb(bloomLevel: string): string {
    const verbs = {
      'Remember': 'recall and identify',
      'Understand': 'explain and describe',
      'Apply': 'implement and demonstrate',
      'Analyze': 'analyze and examine',
      'Evaluate': 'evaluate and critique',
      'Create': 'design and develop'
    };
    return verbs[bloomLevel] || 'understand';
  }

  private generateUnits(subject: string, count: number = 5) {
    const units = [];
    const topics = this.getTopicsForSubject(subject);

    for (let i = 1; i <= count; i++) {
      const unitTopics = topics.slice((i - 1) * 3, i * 3);
      units.push({
        unitNumber: i,
        title: `Unit ${i}: ${this.getUnitTitle(subject, i)}`,
        topics: unitTopics,
        learningOutcomes: [
          `Understand fundamental concepts of ${unitTopics[0] || 'the topic'}`,
          `Apply ${unitTopics[1] || 'theoretical knowledge'} to practical problems`,
          `Analyze different approaches in ${unitTopics[2] || 'the domain'}`
        ],
        contactHours: 12,
        practicalHours: 4,
        assignments: [
          `Assignment ${i}.1: Theoretical problems`,
          `Assignment ${i}.2: Practical implementation`
        ]
      });
    }

    return units;
  }

  private getUnitTitle(subject: string, unitNumber: number): string {
    const titles = {
      'Machine Learning': [
        'Introduction to ML and Data Preprocessing',
        'Supervised Learning Algorithms',
        'Unsupervised Learning and Clustering',
        'Neural Networks and Deep Learning',
        'Advanced Topics and Applications'
      ],
      'Data Structures': [
        'Introduction and Arrays',
        'Linked Lists and Stacks',
        'Trees and Binary Search Trees',
        'Graphs and Graph Algorithms',
        'Hashing and Advanced Data Structures'
      ],
      'Database Management': [
        'Introduction to DBMS',
        'Relational Model and SQL',
        'Normalization and Design',
        'Transaction Management',
        'Advanced Database Concepts'
      ],
      'Computer Networks': [
        'Network Fundamentals',
        'Data Link and Network Layer',
        'Transport Layer Protocols',
        'Application Layer Services',
        'Network Security and Management'
      ]
    };

    const subjectTitles = titles[subject] || [
      'Fundamentals and Introduction',
      'Core Concepts and Theory',
      'Practical Applications',
      'Advanced Topics',
      'Current Trends and Future'
    ];

    return subjectTitles[unitNumber - 1] || `Advanced ${subject} Topics`;
  }

  private getTopicsForSubject(subject: string): string[] {
    const topicMap = {
      'Machine Learning': [
        'Introduction to ML', 'Data Preprocessing', 'Feature Selection',
        'Linear Regression', 'Logistic Regression', 'Decision Trees',
        'K-Means Clustering', 'Hierarchical Clustering', 'DBSCAN',
        'Neural Networks', 'Deep Learning', 'CNN',
        'NLP', 'Computer Vision', 'Reinforcement Learning'
      ],
      'Data Structures': [
        'Arrays', 'Dynamic Arrays', 'Strings',
        'Linked Lists', 'Stacks', 'Queues',
        'Binary Trees', 'BST', 'AVL Trees',
        'Graphs', 'DFS', 'BFS',
        'Hash Tables', 'Heaps', 'Tries'
      ],
      'Database Management': [
        'DBMS Architecture', 'Data Models', 'ER Diagrams',
        'Relational Algebra', 'SQL Queries', 'Joins',
        'Normalization', 'BCNF', 'Functional Dependencies',
        'Transactions', 'ACID Properties', 'Concurrency Control',
        'Indexing', 'Query Optimization', 'NoSQL'
      ],
      'Computer Networks': [
        'Network Topology', 'OSI Model', 'TCP/IP',
        'Ethernet', 'Switching', 'VLANs',
        'IP Addressing', 'Routing', 'OSPF',
        'TCP', 'UDP', 'Flow Control',
        'HTTP', 'DNS', 'Email Protocols'
      ]
    };

    return topicMap[subject] || [
      'Introduction', 'Basic Concepts', 'Fundamentals',
      'Core Theory', 'Practical Applications', 'Implementation',
      'Advanced Topics', 'Case Studies', 'Best Practices',
      'Current Trends', 'Future Directions', 'Industry Applications',
      'Research Areas', 'Emerging Technologies', 'Conclusion'
    ];
  }

  private generateTextBooks(subject: string) {
    const bookMap = {
      'Machine Learning': [
        {
          title: 'Pattern Recognition and Machine Learning',
          authors: ['Christopher M. Bishop'],
          publisher: 'Springer',
          year: 2006,
          isbn: '978-**********',
          edition: '1st'
        },
        {
          title: 'The Elements of Statistical Learning',
          authors: ['Trevor Hastie', 'Robert Tibshirani', 'Jerome Friedman'],
          publisher: 'Springer',
          year: 2009,
          isbn: '978-0387848570',
          edition: '2nd'
        }
      ],
      'Data Structures': [
        {
          title: 'Introduction to Algorithms',
          authors: ['Thomas H. Cormen', 'Charles E. Leiserson', 'Ronald L. Rivest'],
          publisher: 'MIT Press',
          year: 2009,
          isbn: '978-0262033848',
          edition: '3rd'
        },
        {
          title: 'Data Structures and Algorithms in Java',
          authors: ['Robert Lafore'],
          publisher: 'Sams Publishing',
          year: 2017,
          isbn: '978-0672324536',
          edition: '2nd'
        }
      ]
    };

    return bookMap[subject] || [
      {
        title: `Fundamentals of ${subject}`,
        authors: ['Dr. Academic Author'],
        publisher: 'Technical Publications',
        year: 2023,
        isbn: '978-1234567890',
        edition: '1st'
      },
      {
        title: `Advanced ${subject}`,
        authors: ['Prof. Expert Writer'],
        publisher: 'Educational Press',
        year: 2022,
        isbn: '978-0987654321',
        edition: '2nd'
      }
    ];
  }

  async generateCourse(request: CourseGenerationRequest, userPreferences?: { cosCount: number; unitsCount: number }): Promise<Course> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const cosCount = userPreferences?.cosCount || 5;
    const unitsCount = userPreferences?.unitsCount || 5;

    const course: Course = {
      id: this.generateRandomId(),
      title: request.subject,
      code: this.generateCourseCode(request.department, request.semester),
      credits: request.courseType === 'practical' ? 2 : 4,
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: this.getPrerequisites(request.subject, request.semester),
      corequisites: [],
      industryRelevance: {
        skills: this.getIndustrySkills(request.subject),
        tools: this.getIndustryTools(request.subject),
        certifications: this.getRelevantCertifications(request.subject),
        careerOpportunities: this.getCareerOpportunities(request.subject)
      },
      marketDemand: 'High demand in current industry with 25% growth expected',
      difficultyLevel: request.semester <= 4 ? 3 : 4,
      objectives: this.generateObjectives(request.subject),
      outcomes: this.generateCourseOutcomes(request.subject, cosCount),
      units: this.generateUnits(request.subject, unitsCount),
      textBooks: this.generateTextBooks(request.subject),
      referenceBooks: this.generateTextBooks(request.subject).slice(0, 1),
      onlineResources: this.getOnlineResources(request.subject),
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0,
        assignments: 10,
        quizzes: 10,
        midterm: 20
      },
      gradingScheme: 'Absolute grading as per university norms',
      courseDelivery: {
        lectureMethod: 'Interactive lectures with multimedia presentations',
        practicalMethod: 'Hands-on laboratory sessions with industry tools',
        assessmentMethod: 'Continuous assessment with practical evaluations'
      },
      createdAt: new Date(),
      generatedBy: 'Mock AI Course Designer (No API Key Required)'
    };

    return course;
  }

  private getPrerequisites(subject: string, semester: number): string[] {
    if (semester <= 2) return [];
    
    const prereqMap = {
      'Machine Learning': ['Data Structures', 'Statistics', 'Linear Algebra'],
      'Data Structures': ['Programming Fundamentals', 'Mathematics'],
      'Database Management': ['Data Structures', 'Software Engineering'],
      'Computer Networks': ['Operating Systems', 'Computer Organization']
    };

    return prereqMap[subject] || ['Mathematics', 'Programming Fundamentals'];
  }

  private generateObjectives(subject: string) {
    return [
      { id: 1, description: `Understand fundamental concepts and principles of ${subject}` },
      { id: 2, description: `Apply theoretical knowledge to solve practical problems in ${subject}` },
      { id: 3, description: `Analyze different approaches and methodologies in ${subject}` },
      { id: 4, description: `Evaluate solutions and make informed decisions in ${subject} applications` },
      { id: 5, description: `Design and implement systems using ${subject} concepts` }
    ];
  }

  private getIndustrySkills(subject: string): string[] {
    const skillMap = {
      'Machine Learning': ['Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'Data Analysis'],
      'Data Structures': ['Algorithm Design', 'Problem Solving', 'Code Optimization', 'System Design'],
      'Database Management': ['SQL', 'Database Design', 'Query Optimization', 'Data Modeling'],
      'Computer Networks': ['Network Configuration', 'Security', 'Troubleshooting', 'Protocol Analysis']
    };

    return skillMap[subject] || ['Problem Solving', 'Analytical Thinking', 'Technical Communication'];
  }

  private getIndustryTools(subject: string): string[] {
    const toolMap = {
      'Machine Learning': ['Jupyter Notebook', 'Google Colab', 'Anaconda', 'MLflow'],
      'Data Structures': ['IDE', 'Debuggers', 'Profilers', 'Version Control'],
      'Database Management': ['MySQL', 'PostgreSQL', 'MongoDB', 'Oracle'],
      'Computer Networks': ['Wireshark', 'Cisco Packet Tracer', 'GNS3', 'Nmap']
    };

    return toolMap[subject] || ['Development Environment', 'Testing Tools', 'Documentation Tools'];
  }

  private getRelevantCertifications(subject: string): string[] {
    const certMap = {
      'Machine Learning': ['Google ML Engineer', 'AWS ML Specialty', 'Microsoft Azure AI'],
      'Data Structures': ['Coding Certifications', 'Algorithm Competitions', 'Technical Interviews'],
      'Database Management': ['Oracle DBA', 'Microsoft SQL Server', 'MongoDB Certified'],
      'Computer Networks': ['CCNA', 'CompTIA Network+', 'CISSP']
    };

    return certMap[subject] || ['Industry Certification', 'Professional Development'];
  }

  private getCareerOpportunities(subject: string): string[] {
    const careerMap = {
      'Machine Learning': ['ML Engineer', 'Data Scientist', 'AI Researcher', 'ML Consultant'],
      'Data Structures': ['Software Developer', 'Algorithm Engineer', 'System Architect', 'Technical Lead'],
      'Database Management': ['Database Administrator', 'Data Engineer', 'Backend Developer', 'Data Analyst'],
      'Computer Networks': ['Network Engineer', 'Security Analyst', 'System Administrator', 'Network Architect']
    };

    return careerMap[subject] || ['Technical Specialist', 'Consultant', 'Researcher', 'Industry Expert'];
  }

  private getOnlineResources(subject: string): string[] {
    return [
      `Coursera - ${subject} Specialization`,
      `edX - Introduction to ${subject}`,
      `YouTube - ${subject} Tutorial Series`,
      `GitHub - ${subject} Projects and Examples`,
      `Stack Overflow - ${subject} Community`,
      `Medium - ${subject} Articles and Tutorials`
    ];
  }
}
