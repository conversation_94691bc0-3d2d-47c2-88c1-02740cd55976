import { Course, CourseGenerationRequest } from '../types/course';

export class MockLLMService {
  private generateRandomId(): string {
    return `course_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCourseCode(department: string, semester: number): string {
    const deptCode = department.substring(0, 2).toUpperCase();
    const baseCode = 300 + semester;
    return `${deptCode}${baseCode}`;
  }

  private generateCourseOutcomes(subject: string, count: number = 5) {
    const bloomsLevels = ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'];
    const outcomes = [];

    for (let i = 1; i <= count; i++) {
      const bloomLevel = bloomsLevels[Math.min(i - 1, bloomsLevels.length - 1)];
      outcomes.push({
        id: `CO${i}`,
        description: `Students will be able to ${this.getActionVerb(bloomLevel)} key concepts and principles of ${subject}`,
        bloomsLevel: bloomLevel,
        poMapping: [`PO${i}`, `PO${Math.min(i + 1, 12)}`]
      });
    }

    return outcomes;
  }

  private getActionVerb(bloomLevel: string): string {
    const verbs = {
      'Remember': 'recall and identify',
      'Understand': 'explain and describe',
      'Apply': 'implement and demonstrate',
      'Analyze': 'analyze and examine',
      'Evaluate': 'evaluate and critique',
      'Create': 'design and develop'
    };
    return verbs[bloomLevel] || 'understand';
  }

  private generateUnits(subject: string, count: number = 5) {
    const units = [];
    const detailedContent = this.getDetailedCourseContent(subject);

    for (let i = 1; i <= count; i++) {
      // Ensure we have unique content for each unit
      const unitContent = detailedContent.units[i - 1] || this.generateDefaultUnit(subject, i);
      units.push({
        unitNumber: i,
        title: `Unit ${i}: ${unitContent.title}`,
        topics: unitContent.topics,
        topicDescriptions: unitContent.topicDescriptions,
        learningOutcomes: unitContent.learningOutcomes,
        contactHours: unitContent.contactHours || 12,
        practicalHours: unitContent.practicalHours || 4,
        assignments: unitContent.assignments,
        practicalExercises: unitContent.practicalExercises,
        caseStudies: unitContent.caseStudies
      });
    }

    return units;
  }

  private getUnitTitle(subject: string, unitNumber: number): string {
    const titles = {
      'Machine Learning': [
        'Introduction to ML and Data Preprocessing',
        'Supervised Learning Algorithms',
        'Unsupervised Learning and Clustering',
        'Neural Networks and Deep Learning',
        'Advanced Topics and Applications'
      ],
      'Data Structures': [
        'Introduction and Arrays',
        'Linked Lists and Stacks',
        'Trees and Binary Search Trees',
        'Graphs and Graph Algorithms',
        'Hashing and Advanced Data Structures'
      ],
      'Database Management': [
        'Introduction to DBMS',
        'Relational Model and SQL',
        'Normalization and Design',
        'Transaction Management',
        'Advanced Database Concepts'
      ],
      'Computer Networks': [
        'Network Fundamentals',
        'Data Link and Network Layer',
        'Transport Layer Protocols',
        'Application Layer Services',
        'Network Security and Management'
      ]
    };

    const subjectTitles = titles[subject] || [
      'Fundamentals and Introduction',
      'Core Concepts and Theory',
      'Practical Applications',
      'Advanced Topics',
      'Current Trends and Future'
    ];

    return subjectTitles[unitNumber - 1] || `Advanced ${subject} Topics`;
  }

  private getTopicsForSubject(subject: string): string[] {
    const topicMap = {
      'Machine Learning': [
        'Introduction to ML', 'Data Preprocessing', 'Feature Selection',
        'Linear Regression', 'Logistic Regression', 'Decision Trees',
        'K-Means Clustering', 'Hierarchical Clustering', 'DBSCAN',
        'Neural Networks', 'Deep Learning', 'CNN',
        'NLP', 'Computer Vision', 'Reinforcement Learning'
      ],
      'Data Structures': [
        'Arrays', 'Dynamic Arrays', 'Strings',
        'Linked Lists', 'Stacks', 'Queues',
        'Binary Trees', 'BST', 'AVL Trees',
        'Graphs', 'DFS', 'BFS',
        'Hash Tables', 'Heaps', 'Tries'
      ],
      'Database Management': [
        'DBMS Architecture', 'Data Models', 'ER Diagrams',
        'Relational Algebra', 'SQL Queries', 'Joins',
        'Normalization', 'BCNF', 'Functional Dependencies',
        'Transactions', 'ACID Properties', 'Concurrency Control',
        'Indexing', 'Query Optimization', 'NoSQL'
      ],
      'Computer Networks': [
        'Network Topology', 'OSI Model', 'TCP/IP',
        'Ethernet', 'Switching', 'VLANs',
        'IP Addressing', 'Routing', 'OSPF',
        'TCP', 'UDP', 'Flow Control',
        'HTTP', 'DNS', 'Email Protocols'
      ]
    };

    return topicMap[subject] || [
      'Introduction', 'Basic Concepts', 'Fundamentals',
      'Core Theory', 'Practical Applications', 'Implementation',
      'Advanced Topics', 'Case Studies', 'Best Practices',
      'Current Trends', 'Future Directions', 'Industry Applications',
      'Research Areas', 'Emerging Technologies', 'Conclusion'
    ];
  }

  private generateTextBooks(subject: string) {
    const bookMap = {
      'Machine Learning': [
        {
          title: 'Pattern Recognition and Machine Learning',
          authors: ['Christopher M. Bishop'],
          publisher: 'Springer',
          year: 2006,
          isbn: '978-**********',
          edition: '1st'
        },
        {
          title: 'The Elements of Statistical Learning',
          authors: ['Trevor Hastie', 'Robert Tibshirani', 'Jerome Friedman'],
          publisher: 'Springer',
          year: 2009,
          isbn: '978-0387848570',
          edition: '2nd'
        },
        {
          title: 'Machine Learning: A Probabilistic Perspective',
          authors: ['Kevin P. Murphy'],
          publisher: 'MIT Press',
          year: 2012,
          isbn: '978-0262018029',
          edition: '1st'
        }
      ],
      'Data Structures': [
        {
          title: 'Introduction to Algorithms',
          authors: ['Thomas H. Cormen', 'Charles E. Leiserson', 'Ronald L. Rivest', 'Clifford Stein'],
          publisher: 'MIT Press',
          year: 2009,
          isbn: '978-0262033848',
          edition: '3rd'
        },
        {
          title: 'Data Structures and Algorithms in Java',
          authors: ['Robert Lafore'],
          publisher: 'Sams Publishing',
          year: 2017,
          isbn: '978-0672324536',
          edition: '2nd'
        },
        {
          title: 'Algorithms',
          authors: ['Robert Sedgewick', 'Kevin Wayne'],
          publisher: 'Addison-Wesley',
          year: 2011,
          isbn: '978-0321573513',
          edition: '4th'
        }
      ],
      'Database Management': [
        {
          title: 'Database System Concepts',
          authors: ['Abraham Silberschatz', 'Henry F. Korth', 'S. Sudarshan'],
          publisher: 'McGraw-Hill',
          year: 2019,
          isbn: '978-0078022159',
          edition: '7th'
        },
        {
          title: 'Fundamentals of Database Systems',
          authors: ['Ramez Elmasri', 'Shamkant B. Navathe'],
          publisher: 'Pearson',
          year: 2015,
          isbn: '978-0133970777',
          edition: '7th'
        },
        {
          title: 'Database Management Systems',
          authors: ['Raghu Ramakrishnan', 'Johannes Gehrke'],
          publisher: 'McGraw-Hill',
          year: 2002,
          isbn: '978-0072465631',
          edition: '3rd'
        }
      ],
      'Computer Networks': [
        {
          title: 'Computer Networking: A Top-Down Approach',
          authors: ['James F. Kurose', 'Keith W. Ross'],
          publisher: 'Pearson',
          year: 2020,
          isbn: '978-0135928615',
          edition: '8th'
        },
        {
          title: 'Computer Networks',
          authors: ['Andrew S. Tanenbaum', 'David J. Wetherall'],
          publisher: 'Pearson',
          year: 2010,
          isbn: '978-0132126953',
          edition: '5th'
        },
        {
          title: 'TCP/IP Illustrated, Volume 1',
          authors: ['W. Richard Stevens'],
          publisher: 'Addison-Wesley',
          year: 2011,
          isbn: '978-0321336316',
          edition: '2nd'
        }
      ]
    };

    const books = bookMap[subject];
    if (books && books.length >= 3) {
      return books.slice(0, 3); // Return exactly 3 textbooks
    }

    // Default textbooks if subject not found
    return [
      {
        title: `Fundamentals of ${subject}`,
        authors: ['Dr. Academic Author'],
        publisher: 'Technical Publications',
        year: 2023,
        isbn: '978-1234567890',
        edition: '1st'
      },
      {
        title: `Advanced ${subject}`,
        authors: ['Prof. Expert Writer'],
        publisher: 'Educational Press',
        year: 2022,
        isbn: '978-0987654321',
        edition: '2nd'
      },
      {
        title: `Comprehensive Guide to ${subject}`,
        authors: ['Dr. Subject Expert'],
        publisher: 'Academic Press',
        year: 2021,
        isbn: '978-1111111111',
        edition: '1st'
      }
    ];
  }

  private generateReferenceBooks(subject: string) {
    const referenceMap = {
      'Machine Learning': [
        {
          title: 'Hands-On Machine Learning with Scikit-Learn and TensorFlow',
          authors: ['Aurélien Géron'],
          publisher: 'O\'Reilly Media',
          year: 2019,
          isbn: '978-1492032649',
          edition: '2nd'
        },
        {
          title: 'Python Machine Learning',
          authors: ['Sebastian Raschka', 'Vahid Mirjalili'],
          publisher: 'Packt Publishing',
          year: 2019,
          isbn: '978-1789955750',
          edition: '3rd'
        },
        {
          title: 'Deep Learning',
          authors: ['Ian Goodfellow', 'Yoshua Bengio', 'Aaron Courville'],
          publisher: 'MIT Press',
          year: 2016,
          isbn: '978-0262035613',
          edition: '1st'
        }
      ],
      'Data Structures': [
        {
          title: 'Data Structures and Algorithm Analysis in C++',
          authors: ['Mark Allen Weiss'],
          publisher: 'Pearson',
          year: 2013,
          isbn: '978-0132847377',
          edition: '4th'
        },
        {
          title: 'Cracking the Coding Interview',
          authors: ['Gayle Laakmann McDowell'],
          publisher: 'CareerCup',
          year: 2015,
          isbn: '978-0984782857',
          edition: '6th'
        },
        {
          title: 'Algorithm Design Manual',
          authors: ['Steven S. Skiena'],
          publisher: 'Springer',
          year: 2020,
          isbn: '978-3030542559',
          edition: '3rd'
        }
      ],
      'Database Management': [
        {
          title: 'SQL in 10 Minutes, Sams Teach Yourself',
          authors: ['Ben Forta'],
          publisher: 'Sams Publishing',
          year: 2019,
          isbn: '978-0135182796',
          edition: '5th'
        },
        {
          title: 'Learning SQL',
          authors: ['Alan Beaulieu'],
          publisher: 'O\'Reilly Media',
          year: 2020,
          isbn: '978-1492057611',
          edition: '3rd'
        },
        {
          title: 'NoSQL Distilled',
          authors: ['Pramod J. Sadalage', 'Martin Fowler'],
          publisher: 'Addison-Wesley',
          year: 2012,
          isbn: '978-0321826626',
          edition: '1st'
        }
      ],
      'Computer Networks': [
        {
          title: 'Network+ Guide to Networks',
          authors: ['Tamara Dean'],
          publisher: 'Cengage Learning',
          year: 2019,
          isbn: '978-1337569330',
          edition: '8th'
        },
        {
          title: 'Wireshark Network Analysis',
          authors: ['Laura Chappell'],
          publisher: 'Chappell University',
          year: 2017,
          isbn: '978-1893939943',
          edition: '2nd'
        },
        {
          title: 'Network Security Essentials',
          authors: ['William Stallings'],
          publisher: 'Pearson',
          year: 2016,
          isbn: '978-0134527338',
          edition: '6th'
        }
      ]
    };

    const books = referenceMap[subject];
    if (books && books.length >= 3) {
      return books.slice(0, 3); // Return exactly 3 reference books
    }

    // Default reference books if subject not found
    return [
      {
        title: `${subject} Reference Manual`,
        authors: ['Reference Author'],
        publisher: 'Reference Publications',
        year: 2023,
        isbn: '978-2222222222',
        edition: '1st'
      },
      {
        title: `Practical ${subject}`,
        authors: ['Practical Expert'],
        publisher: 'Practical Press',
        year: 2022,
        isbn: '978-3333333333',
        edition: '1st'
      },
      {
        title: `${subject} Handbook`,
        authors: ['Handbook Author'],
        publisher: 'Handbook Publications',
        year: 2021,
        isbn: '978-4444444444',
        edition: '1st'
      }
    ];
  }

  async generateCourse(request: CourseGenerationRequest, userPreferences?: { cosCount: number; unitsCount: number }): Promise<Course> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const cosCount = userPreferences?.cosCount || 5;
    const unitsCount = userPreferences?.unitsCount || 5;

    const course: Course = {
      id: this.generateRandomId(),
      title: request.subject,
      code: this.generateCourseCode(request.department, request.semester),
      credits: request.courseType === 'practical' ? 2 : 4,
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: this.getPrerequisites(request.subject, request.semester),
      corequisites: [],
      industryRelevance: {
        skills: this.getIndustrySkills(request.subject),
        tools: this.getIndustryTools(request.subject),
        certifications: this.getRelevantCertifications(request.subject),
        careerOpportunities: this.getCareerOpportunities(request.subject)
      },
      marketDemand: 'High demand in current industry with 25% growth expected',
      difficultyLevel: request.semester <= 4 ? 3 : 4,
      objectives: this.generateObjectives(request.subject),
      outcomes: this.generateCourseOutcomes(request.subject, cosCount),
      units: this.generateUnits(request.subject, unitsCount),
      textBooks: this.generateTextBooks(request.subject),
      referenceBooks: this.generateReferenceBooks(request.subject),
      onlineResources: this.getOnlineResources(request.subject),
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0,
        assignments: 10,
        quizzes: 10,
        midterm: 20
      },
      gradingScheme: 'Absolute grading as per university norms',
      courseDelivery: {
        lectureMethod: 'Interactive lectures with multimedia presentations',
        practicalMethod: 'Hands-on laboratory sessions with industry tools',
        assessmentMethod: 'Continuous assessment with practical evaluations'
      },
      createdAt: new Date(),
      generatedBy: 'Mock AI Course Designer (No API Key Required)'
    };

    return course;
  }

  private getPrerequisites(subject: string, semester: number): string[] {
    if (semester <= 2) return [];
    
    const prereqMap = {
      'Machine Learning': ['Data Structures', 'Statistics', 'Linear Algebra'],
      'Data Structures': ['Programming Fundamentals', 'Mathematics'],
      'Database Management': ['Data Structures', 'Software Engineering'],
      'Computer Networks': ['Operating Systems', 'Computer Organization']
    };

    return prereqMap[subject] || ['Mathematics', 'Programming Fundamentals'];
  }

  private generateObjectives(subject: string) {
    return [
      { id: 1, description: `Understand fundamental concepts and principles of ${subject}` },
      { id: 2, description: `Apply theoretical knowledge to solve practical problems in ${subject}` },
      { id: 3, description: `Analyze different approaches and methodologies in ${subject}` },
      { id: 4, description: `Evaluate solutions and make informed decisions in ${subject} applications` },
      { id: 5, description: `Design and implement systems using ${subject} concepts` }
    ];
  }

  private getIndustrySkills(subject: string): string[] {
    const skillMap = {
      'Machine Learning': ['Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'Data Analysis'],
      'Data Structures': ['Algorithm Design', 'Problem Solving', 'Code Optimization', 'System Design'],
      'Database Management': ['SQL', 'Database Design', 'Query Optimization', 'Data Modeling'],
      'Computer Networks': ['Network Configuration', 'Security', 'Troubleshooting', 'Protocol Analysis']
    };

    return skillMap[subject] || ['Problem Solving', 'Analytical Thinking', 'Technical Communication'];
  }

  private getIndustryTools(subject: string): string[] {
    const toolMap = {
      'Machine Learning': ['Jupyter Notebook', 'Google Colab', 'Anaconda', 'MLflow'],
      'Data Structures': ['IDE', 'Debuggers', 'Profilers', 'Version Control'],
      'Database Management': ['MySQL', 'PostgreSQL', 'MongoDB', 'Oracle'],
      'Computer Networks': ['Wireshark', 'Cisco Packet Tracer', 'GNS3', 'Nmap']
    };

    return toolMap[subject] || ['Development Environment', 'Testing Tools', 'Documentation Tools'];
  }

  private getRelevantCertifications(subject: string): string[] {
    const certMap = {
      'Machine Learning': ['Google ML Engineer', 'AWS ML Specialty', 'Microsoft Azure AI'],
      'Data Structures': ['Coding Certifications', 'Algorithm Competitions', 'Technical Interviews'],
      'Database Management': ['Oracle DBA', 'Microsoft SQL Server', 'MongoDB Certified'],
      'Computer Networks': ['CCNA', 'CompTIA Network+', 'CISSP']
    };

    return certMap[subject] || ['Industry Certification', 'Professional Development'];
  }

  private getCareerOpportunities(subject: string): string[] {
    const careerMap = {
      'Machine Learning': ['ML Engineer', 'Data Scientist', 'AI Researcher', 'ML Consultant'],
      'Data Structures': ['Software Developer', 'Algorithm Engineer', 'System Architect', 'Technical Lead'],
      'Database Management': ['Database Administrator', 'Data Engineer', 'Backend Developer', 'Data Analyst'],
      'Computer Networks': ['Network Engineer', 'Security Analyst', 'System Administrator', 'Network Architect']
    };

    return careerMap[subject] || ['Technical Specialist', 'Consultant', 'Researcher', 'Industry Expert'];
  }

  private getOnlineResources(subject: string): string[] {
    const resourceMap = {
      'Machine Learning': [
        'https://www.coursera.org/specializations/machine-learning - Andrew Ng\'s Machine Learning Course',
        'https://www.edx.org/course/introduction-to-artificial-intelligence-ai - MIT AI Course',
        'https://www.kaggle.com/learn - Kaggle Learn ML Courses',
        'https://scikit-learn.org/stable/tutorial/index.html - Scikit-learn Official Tutorial',
        'https://www.tensorflow.org/tutorials - TensorFlow Official Tutorials',
        'https://pytorch.org/tutorials/ - PyTorch Official Tutorials'
      ],
      'Data Structures': [
        'https://www.geeksforgeeks.org/data-structures/ - GeeksforGeeks Data Structures',
        'https://www.coursera.org/specializations/data-structures-algorithms - UC San Diego Course',
        'https://visualgo.net/en - Data Structure Visualizations',
        'https://www.hackerrank.com/domains/data-structures - HackerRank Practice',
        'https://leetcode.com/explore/learn/ - LeetCode Learning Path',
        'https://www.youtube.com/playlist?list=PLBZBJbE_rGRV8D7XZ08LK6z-4zPoWzu5H - CS Dojo YouTube'
      ],
      'Database Management': [
        'https://www.w3schools.com/sql/ - W3Schools SQL Tutorial',
        'https://www.coursera.org/specializations/database-systems - Database Systems Specialization',
        'https://www.postgresql.org/docs/current/tutorial.html - PostgreSQL Tutorial',
        'https://dev.mysql.com/doc/mysql-tutorial-excerpt/8.0/en/ - MySQL Tutorial',
        'https://www.mongodb.com/university - MongoDB University',
        'https://sqlbolt.com/ - Interactive SQL Tutorial'
      ],
      'Computer Networks': [
        'https://www.coursera.org/learn/computer-networking - Computer Networking Course',
        'https://www.cisco.com/c/en/us/training-events/training-certifications/certifications/associate/ccna.html - CCNA Training',
        'https://www.wireshark.org/docs/ - Wireshark Documentation',
        'https://www.youtube.com/playlist?list=PLowKtXNTBypH19whXTVoG3oKSuOcw_XeW - Ben Eater Networking',
        'https://tools.ietf.org/rfc/ - Internet Engineering Task Force RFCs',
        'https://www.khanacademy.org/computing/computers-and-internet - Khan Academy Networking'
      ]
    };

    return resourceMap[subject] || [
      `https://www.coursera.org/search?query=${encodeURIComponent(subject)} - Coursera ${subject} Courses`,
      `https://www.edx.org/search?q=${encodeURIComponent(subject)} - edX ${subject} Courses`,
      `https://www.youtube.com/results?search_query=${encodeURIComponent(subject)}+tutorial - YouTube Tutorials`,
      `https://github.com/search?q=${encodeURIComponent(subject)} - GitHub Projects`,
      `https://stackoverflow.com/questions/tagged/${subject.toLowerCase().replace(/\s+/g, '-')} - Stack Overflow`,
      `https://medium.com/search?q=${encodeURIComponent(subject)} - Medium Articles`
    ];
  }

  private getDetailedCourseContent(subject: string) {
    const contentMap = {
      'Machine Learning': {
        units: [
          {
            title: 'Introduction to Machine Learning and Mathematical Foundations',
            topics: [
              'Supervised vs Unsupervised vs Reinforcement Learning',
              'Vector Operations and Matrix Multiplication',
              'Probability Distributions and Bayes Theorem',
              'Feature Scaling and Normalization Techniques',
              'Principal Component Analysis (PCA)'
            ],
            topicDescriptions: {
              'Supervised vs Unsupervised vs Reinforcement Learning': 'Classification of ML paradigms with examples: regression, clustering, Q-learning.',
              'Vector Operations and Matrix Multiplication': 'Dot products, matrix operations, eigenvalues, eigenvectors for ML computations.',
              'Probability Distributions and Bayes Theorem': 'Gaussian, Bernoulli, Multinomial distributions, conditional probability, Bayes rule.',
              'Feature Scaling and Normalization Techniques': 'Min-max scaling, Z-score normalization, robust scaling for data preprocessing.',
              'Principal Component Analysis (PCA)': 'Dimensionality reduction, variance maximization, eigenvalue decomposition.'
            },
            learningOutcomes: [
              'Understand the fundamental concepts and applications of machine learning',
              'Apply mathematical foundations including linear algebra and statistics to ML problems',
              'Implement data preprocessing techniques for real-world datasets'
            ],
            contactHours: 15,
            practicalHours: 6,
            assignments: [
              'Mathematical foundations quiz and problem solving',
              'Data preprocessing project using Python/R',
              'Exploratory data analysis on a real dataset'
            ],
            practicalExercises: [
              'Implementing basic statistical functions in Python',
              'Data visualization using matplotlib and seaborn',
              'Feature engineering on Titanic dataset'
            ],
            caseStudies: [
              'Netflix recommendation system overview',
              'Google search algorithm basics'
            ]
          },
          {
            title: 'Supervised Learning Algorithms',
            topics: [
              'Linear Regression with Gradient Descent',
              'Logistic Regression and Sigmoid Function',
              'Decision Tree Construction and Pruning',
              'Support Vector Machine with Kernel Trick',
              'K-Nearest Neighbors Algorithm'
            ],
            topicDescriptions: {
              'Linear Regression with Gradient Descent': 'Cost function minimization, batch and stochastic gradient descent, learning rate optimization.',
              'Logistic Regression and Sigmoid Function': 'Binary classification, sigmoid activation, maximum likelihood estimation, regularization.',
              'Decision Tree Construction and Pruning': 'Information gain, Gini impurity, tree splitting criteria, post-pruning techniques.',
              'Support Vector Machine with Kernel Trick': 'Hyperplane separation, soft margin, RBF kernel, polynomial kernel, parameter tuning.',
              'K-Nearest Neighbors Algorithm': 'Distance metrics (Euclidean, Manhattan), k-value selection, weighted voting, curse of dimensionality.'
            },
            learningOutcomes: [
              'Implement and compare various supervised learning algorithms',
              'Evaluate model performance using appropriate metrics',
              'Select optimal algorithms for specific problem domains'
            ],
            contactHours: 18,
            practicalHours: 8,
            assignments: [
              'Comparative analysis of classification algorithms',
              'Regression analysis on housing price prediction',
              'Model selection and hyperparameter tuning project'
            ],
            practicalExercises: [
              'Implementing linear regression from scratch',
              'Building decision trees using scikit-learn',
              'SVM implementation for text classification'
            ],
            caseStudies: [
              'Credit card fraud detection system',
              'Medical diagnosis using ML algorithms'
            ]
          },
          {
            title: 'Unsupervised Learning and Clustering',
            topics: [
              'K-Means Clustering Algorithm',
              'Hierarchical Clustering with Dendrograms',
              'DBSCAN Density-Based Clustering',
              'Apriori Algorithm for Association Rules',
              'Gaussian Mixture Models (GMM)'
            ],
            topicDescriptions: {
              'K-Means Clustering Algorithm': 'Centroid initialization, Lloyd\'s algorithm, K-means++, elbow method, silhouette coefficient.',
              'Hierarchical Clustering with Dendrograms': 'Agglomerative clustering, single/complete/average linkage, dendrogram interpretation.',
              'DBSCAN Density-Based Clustering': 'Core points, border points, noise detection, epsilon and min-points parameters.',
              'Apriori Algorithm for Association Rules': 'Frequent itemset mining, support and confidence thresholds, lift and conviction measures.',
              'Gaussian Mixture Models (GMM)': 'Expectation-Maximization algorithm, probabilistic clustering, model selection with BIC/AIC.'
            },
            learningOutcomes: [
              'Apply clustering algorithms to discover patterns in unlabeled data',
              'Implement dimensionality reduction techniques for data visualization',
              'Extract association rules from transactional data'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Customer segmentation using clustering algorithms',
              'Dimensionality reduction on high-dimensional datasets',
              'Market basket analysis project'
            ],
            practicalExercises: [
              'K-means clustering on customer data',
              'PCA implementation for image compression',
              'Association rule mining on retail data'
            ],
            caseStudies: [
              'Customer segmentation for e-commerce',
              'Gene expression analysis using clustering'
            ]
          },
          {
            title: 'Neural Networks and Deep Learning',
            topics: [
              'Artificial Neural Networks',
              'Backpropagation Algorithm',
              'Convolutional Neural Networks (CNN)',
              'Recurrent Neural Networks (RNN)',
              'Deep Learning Frameworks'
            ],
            topicDescriptions: {
              'Artificial Neural Networks': 'Perceptron, multi-layer perceptrons, activation functions, and network architectures.',
              'Backpropagation Algorithm': 'Gradient computation, chain rule, weight updates, and optimization techniques.',
              'Convolutional Neural Networks (CNN)': 'Convolution layers, pooling, feature maps, and applications in computer vision.',
              'Recurrent Neural Networks (RNN)': 'LSTM, GRU, sequence modeling, and applications in NLP.',
              'Deep Learning Frameworks': 'TensorFlow, PyTorch, Keras, and model deployment strategies.'
            },
            learningOutcomes: [
              'Design and implement neural network architectures',
              'Apply deep learning techniques to computer vision and NLP problems',
              'Use modern deep learning frameworks for model development'
            ],
            contactHours: 20,
            practicalHours: 10,
            assignments: [
              'Neural network implementation from scratch',
              'Image classification using CNN',
              'Text sentiment analysis using RNN'
            ],
            practicalExercises: [
              'Building neural networks with TensorFlow',
              'CNN for handwritten digit recognition',
              'LSTM for stock price prediction'
            ],
            caseStudies: [
              'AlexNet and ImageNet competition',
              'Google Translate neural machine translation'
            ]
          },
          {
            title: 'Advanced Topics and Applications',
            topics: [
              'Ensemble Methods',
              'Natural Language Processing',
              'Computer Vision Applications',
              'Reinforcement Learning Basics',
              'ML Model Deployment and MLOps'
            ],
            topicDescriptions: {
              'Ensemble Methods': 'Bagging, boosting, AdaBoost, Gradient Boosting, and XGBoost algorithms.',
              'Natural Language Processing': 'Text preprocessing, TF-IDF, word embeddings, and transformer models.',
              'Computer Vision Applications': 'Object detection, image segmentation, face recognition, and OpenCV.',
              'Reinforcement Learning Basics': 'Q-learning, policy gradients, and applications in game playing.',
              'ML Model Deployment and MLOps': 'Model serving, monitoring, versioning, and production pipelines.'
            },
            learningOutcomes: [
              'Implement ensemble methods for improved model performance',
              'Apply ML techniques to NLP and computer vision problems',
              'Deploy ML models in production environments'
            ],
            contactHours: 18,
            practicalHours: 8,
            assignments: [
              'Ensemble methods comparison project',
              'NLP application development',
              'End-to-end ML project with deployment'
            ],
            practicalExercises: [
              'XGBoost for structured data prediction',
              'Text classification with transformers',
              'Model deployment using Flask/FastAPI'
            ],
            caseStudies: [
              'Kaggle competition winning solutions',
              'Industry ML deployment case studies'
            ]
          }
        ]
      },
      'Data Structures': {
        units: [
          {
            title: 'Introduction to Data Structures and Algorithm Analysis',
            topics: [
              'Abstract Data Types (ADT)',
              'Big O Notation and Complexity Analysis',
              'Array Operations and Memory Layout',
              'String Matching Algorithms',
              'Pointer Arithmetic and Memory Management'
            ],
            topicDescriptions: {
              'Abstract Data Types (ADT)': 'Interface vs implementation, encapsulation, data abstraction, ADT specifications.',
              'Big O Notation and Complexity Analysis': 'Time complexity, space complexity, best/average/worst case, asymptotic analysis.',
              'Array Operations and Memory Layout': 'Array indexing, insertion, deletion, searching, memory allocation, cache locality.',
              'String Matching Algorithms': 'Naive string matching, KMP algorithm, Boyer-Moore algorithm, Rabin-Karp algorithm.',
              'Pointer Arithmetic and Memory Management': 'Memory allocation, deallocation, pointer operations, memory leaks, garbage collection.'
            },
            learningOutcomes: [
              'Analyze time and space complexity of algorithms using Big O notation',
              'Implement efficient array-based data structures',
              'Apply string processing algorithms for text manipulation'
            ],
            contactHours: 12,
            practicalHours: 6,
            assignments: [
              'Algorithm complexity analysis problems',
              'Dynamic array implementation in C++/Java',
              'String processing algorithms implementation'
            ],
            practicalExercises: [
              'Memory allocation and deallocation exercises',
              'Array sorting and searching implementations',
              'Pattern matching algorithm coding'
            ],
            caseStudies: [
              'Google search indexing using arrays',
              'Database indexing strategies'
            ]
          },
          {
            title: 'Linear Data Structures',
            topics: [
              'Stack Implementation and Applications',
              'Queue Implementation with Circular Arrays',
              'Singly and Doubly Linked Lists',
              'Priority Queue with Binary Heap',
              'Deque (Double-Ended Queue)'
            ],
            topicDescriptions: {
              'Stack Implementation and Applications': 'LIFO operations, array-based and linked implementation, expression evaluation, function call stack.',
              'Queue Implementation with Circular Arrays': 'FIFO operations, circular array implementation, front and rear pointers, queue overflow handling.',
              'Singly and Doubly Linked Lists': 'Node structure, insertion/deletion operations, traversal, memory allocation, pointer manipulation.',
              'Priority Queue with Binary Heap': 'Heap property, heapify operations, insertion and deletion in O(log n), heap sort algorithm.',
              'Deque (Double-Ended Queue)': 'Double-ended operations, circular buffer implementation, applications in sliding window problems.'
            },
            learningOutcomes: [
              'Implement and apply stack data structure for problem solving',
              'Design efficient queue-based solutions for real-world problems',
              'Compare different linked list implementations and their use cases'
            ],
            contactHours: 14,
            practicalHours: 8,
            assignments: [
              'Stack-based expression evaluator',
              'Queue simulation for operating systems',
              'Linked list implementation with memory management'
            ],
            practicalExercises: [
              'Balanced parentheses checker using stacks',
              'BFS implementation using queues',
              'Music playlist using doubly linked lists'
            ],
            caseStudies: [
              'Browser history implementation',
              'Print queue management system'
            ]
          },
          {
            title: 'Trees and Tree Algorithms',
            topics: [
              'Binary Tree Traversal Algorithms',
              'Binary Search Tree Operations',
              'AVL Tree Rotations and Balancing',
              'Heap Sort Algorithm',
              'Huffman Coding Algorithm'
            ],
            topicDescriptions: {
              'Binary Tree Traversal Algorithms': 'Inorder, preorder, postorder recursive and iterative implementations, level-order traversal using queue.',
              'Binary Search Tree Operations': 'BST insertion, deletion, search operations, finding min/max, inorder successor/predecessor.',
              'AVL Tree Rotations and Balancing': 'Left rotation, right rotation, left-right and right-left rotations, balance factor calculation.',
              'Heap Sort Algorithm': 'Build max-heap, extract maximum, heapify operation, in-place sorting with O(n log n) complexity.',
              'Huffman Coding Algorithm': 'Frequency-based encoding, building Huffman tree, optimal prefix codes, compression applications.'
            },
            learningOutcomes: [
              'Implement various tree data structures and traversal algorithms',
              'Design self-balancing tree structures for optimal performance',
              'Apply heap data structure for priority-based operations'
            ],
            contactHours: 16,
            practicalHours: 10,
            assignments: [
              'BST implementation with all operations',
              'AVL tree with automatic balancing',
              'Heap-based priority queue system'
            ],
            practicalExercises: [
              'File system directory structure using trees',
              'Expression evaluation using expression trees',
              'Huffman coding using binary trees'
            ],
            caseStudies: [
              'Database indexing using B-trees',
              'Compiler syntax tree construction'
            ]
          },
          {
            title: 'Graphs and Graph Algorithms',
            topics: [
              'Depth-First Search (DFS) Algorithm',
              'Breadth-First Search (BFS) Algorithm',
              'Dijkstra\'s Shortest Path Algorithm',
              'Kruskal\'s Minimum Spanning Tree',
              'Floyd-Warshall All-Pairs Shortest Path'
            ],
            topicDescriptions: {
              'Depth-First Search (DFS) Algorithm': 'Recursive and iterative DFS implementation, DFS tree, cycle detection, topological sorting.',
              'Breadth-First Search (BFS) Algorithm': 'Queue-based BFS implementation, shortest path in unweighted graphs, level-order traversal.',
              'Dijkstra\'s Shortest Path Algorithm': 'Single-source shortest path, priority queue implementation, relaxation technique, path reconstruction.',
              'Kruskal\'s Minimum Spanning Tree': 'Edge-based MST algorithm, union-find data structure, cycle detection, greedy approach.',
              'Floyd-Warshall All-Pairs Shortest Path': 'Dynamic programming approach, all-pairs shortest distances, transitive closure, negative cycle detection.'
            },
            learningOutcomes: [
              'Implement graph data structures and basic operations',
              'Apply graph traversal algorithms for connectivity problems',
              'Solve shortest path and minimum spanning tree problems'
            ],
            contactHours: 18,
            practicalHours: 10,
            assignments: [
              'Social network analysis using graphs',
              'Route planning system using shortest path algorithms',
              'Network design using MST algorithms'
            ],
            practicalExercises: [
              'DFS and BFS implementation on various graph types',
              'Dijkstra\'s algorithm for GPS navigation',
              'Kruskal\'s algorithm for network optimization'
            ],
            caseStudies: [
              'Google Maps routing algorithms',
              'Social media friend recommendation systems'
            ]
          },
          {
            title: 'Advanced Data Structures and Hashing',
            topics: [
              'Hash Tables and Hash Functions',
              'Collision Resolution Techniques',
              'Advanced Tree Structures',
              'Trie Data Structure',
              'Disjoint Set Union (DSU)'
            ],
            topicDescriptions: {
              'Hash Tables and Hash Functions': 'Hash table design, hash function properties, load factor, and performance analysis.',
              'Collision Resolution Techniques': 'Chaining, open addressing, linear probing, quadratic probing, and double hashing.',
              'Advanced Tree Structures': 'B-trees, B+ trees, Red-Black trees, and their applications in databases and file systems.',
              'Trie Data Structure': 'Prefix trees, string storage and retrieval, autocomplete systems, and compressed tries.',
              'Disjoint Set Union (DSU)': 'Union-Find data structure, path compression, union by rank, and applications in connectivity problems.'
            },
            learningOutcomes: [
              'Design efficient hash-based data structures',
              'Implement advanced tree structures for specialized applications',
              'Apply trie data structure for string processing problems'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Hash table implementation with collision handling',
              'Autocomplete system using trie data structure',
              'Union-Find for dynamic connectivity problems'
            ],
            practicalExercises: [
              'Dictionary implementation using hash tables',
              'Search engine indexing using tries',
              'Network connectivity using DSU'
            ],
            caseStudies: [
              'Database indexing using B+ trees',
              'Spell checker implementation using tries'
            ]
          }
        ]
      },
      'Operating Systems': {
        units: [
          {
            title: 'Process Management and CPU Scheduling',
            topics: [
              'Process Control Block (PCB)',
              'First Come First Serve (FCFS) Scheduling',
              'Shortest Job First (SJF) Scheduling',
              'Round Robin Scheduling Algorithm',
              'Priority Scheduling with Aging'
            ],
            topicDescriptions: {
              'Process Control Block (PCB)': 'Process state, program counter, CPU registers, memory management information, I/O status.',
              'First Come First Serve (FCFS) Scheduling': 'Non-preemptive scheduling, convoy effect, average waiting time calculation.',
              'Shortest Job First (SJF) Scheduling': 'Preemptive and non-preemptive SJF, optimal average waiting time, starvation problem.',
              'Round Robin Scheduling Algorithm': 'Time quantum selection, context switching overhead, response time optimization.',
              'Priority Scheduling with Aging': 'Static and dynamic priorities, priority inversion, aging technique to prevent starvation.'
            },
            learningOutcomes: [
              'Implement various CPU scheduling algorithms',
              'Calculate performance metrics for scheduling algorithms',
              'Analyze trade-offs between different scheduling policies'
            ],
            contactHours: 14,
            practicalHours: 8,
            assignments: [
              'CPU scheduling simulator implementation',
              'Performance comparison of scheduling algorithms',
              'Process synchronization project'
            ],
            practicalExercises: [
              'FCFS and SJF algorithm implementation',
              'Round Robin scheduler with time quantum',
              'Priority queue implementation for scheduling'
            ],
            caseStudies: [
              'Linux CFS (Completely Fair Scheduler)',
              'Windows thread scheduling'
            ]
          },
          {
            title: 'Memory Management Techniques',
            topics: [
              'Paging and Page Replacement Algorithms',
              'FIFO Page Replacement Algorithm',
              'LRU (Least Recently Used) Algorithm',
              'Optimal Page Replacement Algorithm',
              'Segmentation and Virtual Memory'
            ],
            topicDescriptions: {
              'Paging and Page Replacement Algorithms': 'Page tables, page faults, demand paging, page replacement strategies.',
              'FIFO Page Replacement Algorithm': 'First-in-first-out replacement, Belady\'s anomaly, implementation using queue.',
              'LRU (Least Recently Used) Algorithm': 'Stack-based implementation, counter-based implementation, approximation algorithms.',
              'Optimal Page Replacement Algorithm': 'Belady\'s optimal algorithm, theoretical minimum page faults, comparison benchmark.',
              'Segmentation and Virtual Memory': 'Logical vs physical addresses, segment tables, memory protection, virtual address translation.'
            },
            learningOutcomes: [
              'Implement page replacement algorithms',
              'Analyze memory management performance',
              'Design virtual memory systems'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Page replacement algorithm simulator',
              'Virtual memory management system',
              'Memory allocation strategies comparison'
            ],
            practicalExercises: [
              'LRU cache implementation',
              'Page table management',
              'Memory fragmentation analysis'
            ],
            caseStudies: [
              'Linux memory management',
              'Android memory optimization'
            ]
          }
        ]
      },
      'Computer Networks': {
        units: [
          {
            title: 'Network Protocols and OSI Model',
            topics: [
              'OSI Seven Layer Model',
              'TCP Three-Way Handshake',
              'UDP Protocol Implementation',
              'HTTP Request-Response Cycle',
              'DNS Resolution Process'
            ],
            topicDescriptions: {
              'OSI Seven Layer Model': 'Physical, Data Link, Network, Transport, Session, Presentation, Application layers and their functions.',
              'TCP Three-Way Handshake': 'SYN, SYN-ACK, ACK sequence, connection establishment, sequence numbers, reliability.',
              'UDP Protocol Implementation': 'Connectionless communication, datagram structure, checksum calculation, applications.',
              'HTTP Request-Response Cycle': 'HTTP methods (GET, POST, PUT, DELETE), status codes, headers, cookies, sessions.',
              'DNS Resolution Process': 'Domain name hierarchy, recursive and iterative queries, DNS caching, record types (A, AAAA, CNAME).'
            },
            learningOutcomes: [
              'Implement network protocol stacks',
              'Analyze network communication patterns',
              'Design reliable communication protocols'
            ],
            contactHours: 15,
            practicalHours: 10,
            assignments: [
              'TCP socket programming project',
              'HTTP server implementation',
              'Network packet analyzer'
            ],
            practicalExercises: [
              'Wireshark packet capture analysis',
              'Socket programming in C/Python',
              'DNS lookup implementation'
            ],
            caseStudies: [
              'Internet backbone architecture',
              'CDN (Content Delivery Network) design'
            ]
          }
        ]
      }
    };

    return contentMap[subject] || {
      units: [
        {
          title: 'Fundamentals and Core Concepts',
          topics: [`Introduction to ${subject}`, `Basic Principles`, `Theoretical Foundations`, `Practical Applications`, `Current Trends`],
          topicDescriptions: {
            [`Introduction to ${subject}`]: `Comprehensive overview of ${subject} including history, applications, and current trends.`,
            'Basic Principles': `Fundamental principles and methodologies underlying ${subject}.`,
            'Theoretical Foundations': `Mathematical and theoretical concepts essential for understanding ${subject}.`,
            'Practical Applications': `Real-world applications and use cases of ${subject} in industry.`,
            'Current Trends': `Latest developments and emerging trends in ${subject}.`
          },
          learningOutcomes: [
            `Understand the fundamental concepts of ${subject}`,
            `Apply basic principles to solve problems`,
            `Analyze theoretical foundations`
          ],
          contactHours: 12,
          practicalHours: 4,
          assignments: [`${subject} fundamentals assignment`, `Theoretical problem solving`],
          practicalExercises: [`Hands-on ${subject} exercises`],
          caseStudies: [`Real-world ${subject} applications`]
        }
      ]
    };
  }

  private generateDefaultUnit(subject: string, unitNumber: number) {
    const unitTitles = [
      'Fundamentals and Core Concepts',
      'Advanced Principles and Methodologies',
      'Practical Applications and Implementation',
      'Advanced Topics and Techniques',
      'Industry Applications and Future Trends'
    ];

    const baseTopics = [
      [`Introduction to ${subject}`, `Basic Principles`, `Theoretical Foundations`, `Practical Applications`, `Current Trends`],
      [`Advanced Concepts`, `Design Patterns`, `Best Practices`, `Performance Optimization`, `Quality Assurance`],
      [`Implementation Strategies`, `Tools and Technologies`, `Project Management`, `Testing and Validation`, `Deployment`],
      [`Advanced Algorithms`, `Optimization Techniques`, `Security Considerations`, `Scalability Issues`, `Integration`],
      [`Industry Standards`, `Emerging Technologies`, `Future Directions`, `Research Trends`, `Career Opportunities`]
    ];

    const topicIndex = (unitNumber - 1) % baseTopics.length;
    const topics = baseTopics[topicIndex];

    return {
      title: unitTitles[topicIndex] || `Unit ${unitNumber}: Advanced Topics`,
      topics: topics,
      topicDescriptions: topics.reduce((acc, topic) => {
        acc[topic] = `Comprehensive coverage of ${topic.toLowerCase()} in the context of ${subject}.`;
        return acc;
      }, {}),
      learningOutcomes: [
        `Understand and apply ${topics[0].toLowerCase()}`,
        `Implement ${topics[1].toLowerCase()} effectively`,
        `Analyze ${topics[2].toLowerCase()} in real-world scenarios`
      ],
      contactHours: 12,
      practicalHours: 4,
      assignments: [
        `${topics[0]} analysis assignment`,
        `${topics[1]} implementation project`
      ],
      practicalExercises: [
        `Hands-on ${topics[2]} exercises`,
        `${topics[3]} practical implementation`
      ],
      caseStudies: [
        `Industry case study on ${topics[4]}`,
        `Real-world application of ${subject}`
      ]
    };
  }
}
