// Updated course content with specific technical topics - All syntax errors fixed
import { Course, CourseGenerationRequest } from '../types/course';

export class MockLLMService {
  private generateRandomId(): string {
    return `course_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCourseCode(department: string, semester: number): string {
    const deptCode = department.substring(0, 2).toUpperCase();
    const baseCode = 300 + semester;
    return `${deptCode}${baseCode}`;
  }

  private generateCourseOutcomes(subject: string, count: number = 5) {
    const bloomsLevels = ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'];
    const outcomes = [];

    for (let i = 1; i <= count; i++) {
      const bloomLevel = bloomsLevels[Math.min(i - 1, bloomsLevels.length - 1)];
      outcomes.push({
        id: `CO${i}`,
        description: `Students will be able to ${this.getActionVerb(bloomLevel)} key concepts and principles of ${subject}`,
        bloomsLevel: bloomLevel,
        poMapping: [`PO${i}`, `PO${Math.min(i + 1, 12)}`]
      });
    }

    return outcomes;
  }

  private getActionVerb(bloomLevel: string): string {
    const verbs = {
      'Remember': 'recall and identify',
      'Understand': 'explain and describe',
      'Apply': 'implement and demonstrate',
      'Analyze': 'analyze and examine',
      'Evaluate': 'evaluate and critique',
      'Create': 'design and develop'
    };
    return verbs[bloomLevel] || 'understand';
  }

  private generateUnits(subject: string, count: number = 5) {
    const units = [];
    const detailedContent = this.getDetailedCourseContent(subject);

    for (let i = 1; i <= count; i++) {
      let unitContent;

      if (i <= detailedContent.units.length) {
        // Use detailed content for available units
        unitContent = detailedContent.units[i - 1];
      } else if (detailedContent.hasDetailedContent) {
        // For subjects with detailed content, intelligently extend with related advanced topics
        unitContent = this.generateAdvancedUnit(subject, i, detailedContent.units);
      } else {
        // Fallback to default unit generation
        unitContent = this.generateDefaultUnit(subject, i);
      }

      units.push({
        unitNumber: i,
        title: `Unit ${i}: ${unitContent.title}`,
        topics: unitContent.topics,
        topicDescriptions: unitContent.topicDescriptions,
        learningOutcomes: unitContent.learningOutcomes,
        contactHours: unitContent.contactHours || 12,
        practicalHours: unitContent.practicalHours || 4,
        assignments: unitContent.assignments,
        practicalExercises: unitContent.practicalExercises,
        caseStudies: unitContent.caseStudies
      });
    }

    return units;
  }

  private generateAdvancedUnit(subject: string, unitNumber: number, existingUnits: any[]) {
    // Generate advanced units based on the subject and existing content
    const advancedTopicsMap: { [key: string]: string[][] } = {
      'operating systems': [
        [
          'Kernel Debugging: GDB, KGDB, Crash Dump Analysis',
          'System Call Optimization: Fast System Calls, vDSO',
          'Advanced Scheduling: CFS Internals, RT Scheduling',
          'Memory Management Internals: Slab Allocator, NUMA',
          'File System Internals: VFS Layer, Journaling'
        ],
        [
          'Distributed Operating Systems: Cluster Management',
          'Fault Tolerance: Checkpointing, Process Migration',
          'High Availability: Redundancy, Failover Mechanisms',
          'Performance Tuning: Kernel Parameters, Optimization',
          'Security Hardening: SELinux, AppArmor, Capabilities'
        ],
        [
          'Research Topics: Exokernel, Microkernel Performance',
          'Emerging Technologies: Quantum Computing OS',
          'Industry Case Studies: Google Borg, Facebook TAO',
          'Future Directions: AI-driven Resource Management',
          'Professional Skills: System Administration, DevOps'
        ]
      ],
      'machine learning': [
        [
          'Advanced Deep Learning: GANs, VAEs, Transformers',
          'Reinforcement Learning: Deep Q-Networks, Policy Gradients',
          'Meta-Learning: Few-shot Learning, Model-Agnostic Meta-Learning',
          'Federated Learning: Privacy-preserving ML, Edge Computing',
          'AutoML: Neural Architecture Search, Hyperparameter Optimization'
        ],
        [
          'MLOps: Model Deployment, Monitoring, A/B Testing',
          'Explainable AI: LIME, SHAP, Attention Visualization',
          'Adversarial ML: Attacks, Defenses, Robustness',
          'Quantum Machine Learning: Quantum Algorithms, NISQ',
          'Neuromorphic Computing: Spiking Neural Networks'
        ]
      ],
      'computer networks': [
        [
          'Software Defined Networking: OpenFlow, Network Virtualization',
          'Network Function Virtualization: Service Chaining',
          'Intent-based Networking: Policy Automation',
          '5G Networks: Network Slicing, Edge Computing',
          'Quantum Networking: Quantum Key Distribution'
        ],
        [
          'Network Security: Zero Trust, SASE Architecture',
          'IoT Networking: LoRaWAN, NB-IoT, Edge Protocols',
          'Blockchain Networks: Consensus Protocols, DLT',
          'AI in Networking: ML-driven Optimization',
          'Future Networks: 6G Research, Terahertz Communication'
        ]
      ],
      'data structures': [
        [
          'Advanced Tree Structures: Red-Black Trees, Splay Trees',
          'Persistent Data Structures: Functional Programming',
          'Concurrent Data Structures: Lock-free Programming',
          'Cache-Oblivious Algorithms: External Memory Models',
          'Succinct Data Structures: Compressed Representations'
        ],
        [
          'Geometric Data Structures: Range Trees, Segment Trees',
          'String Algorithms: Suffix Arrays, Burrows-Wheeler Transform',
          'Probabilistic Data Structures: Bloom Filters, Skip Lists',
          'Parallel Algorithms: MapReduce, GPU Computing',
          'Advanced Applications: Bioinformatics, Computational Geometry'
        ]
      ]
    };

    const normalizedSubject = subject.toLowerCase().replace(/[^a-z\s]/g, '').trim();
    const advancedTopics = advancedTopicsMap[normalizedSubject];

    if (advancedTopics && advancedTopics.length > 0) {
      const topicIndex = (unitNumber - existingUnits.length - 1) % advancedTopics.length;
      const topics = advancedTopics[topicIndex];

      return {
        title: `Advanced ${subject} Topics ${unitNumber - existingUnits.length}`,
        topics: topics,
        topicDescriptions: topics.reduce((acc: any, topic: string) => {
          acc[topic] = `Advanced study of ${topic} including cutting-edge research, industry applications, and future trends.`;
          return acc;
        }, {}),
        learningOutcomes: [
          `Master advanced concepts in ${subject}`,
          `Apply cutting-edge techniques to complex problems`,
          `Evaluate and critique current research in the field`
        ],
        contactHours: 15,
        practicalHours: 8,
        assignments: topics.slice(0, 3).map((topic: string) => `Advanced ${topic} research project`),
        practicalExercises: topics.slice(0, 4).map((topic: string) => `Hands-on implementation of ${topic}`),
        caseStudies: [
          `Industry case study on advanced ${subject} applications`,
          `Research paper analysis and critique`
        ]
      };
    }

    // Fallback to default unit if no advanced topics defined
    return this.generateDefaultUnit(subject, unitNumber);
  }

  private getUnitTitle(subject: string, unitNumber: number): string {
    const titles = {
      'Machine Learning': [
        'Introduction to ML and Data Preprocessing',
        'Supervised Learning Algorithms',
        'Unsupervised Learning and Clustering',
        'Neural Networks and Deep Learning',
        'Advanced Topics and Applications'
      ],
      'Data Structures': [
        'Introduction and Arrays',
        'Linked Lists and Stacks',
        'Trees and Binary Search Trees',
        'Graphs and Graph Algorithms',
        'Hashing and Advanced Data Structures'
      ],
      'Database Management': [
        'Introduction to DBMS',
        'Relational Model and SQL',
        'Normalization and Design',
        'Transaction Management',
        'Advanced Database Concepts'
      ],
      'Computer Networks': [
        'Network Fundamentals',
        'Data Link and Network Layer',
        'Transport Layer Protocols',
        'Application Layer Services',
        'Network Security and Management'
      ]
    };

    const subjectTitles = titles[subject] || [
      'Fundamentals and Introduction',
      'Core Concepts and Theory',
      'Practical Applications',
      'Advanced Topics',
      'Current Trends and Future'
    ];

    return subjectTitles[unitNumber - 1] || `Advanced ${subject} Topics`;
  }

  private getTopicsForSubject(subject: string): string[] {
    const topicMap = {
      'Machine Learning': [
        'Introduction to ML', 'Data Preprocessing', 'Feature Selection',
        'Linear Regression', 'Logistic Regression', 'Decision Trees',
        'K-Means Clustering', 'Hierarchical Clustering', 'DBSCAN',
        'Neural Networks', 'Deep Learning', 'CNN',
        'NLP', 'Computer Vision', 'Reinforcement Learning'
      ],
      'Data Structures': [
        'Arrays', 'Dynamic Arrays', 'Strings',
        'Linked Lists', 'Stacks', 'Queues',
        'Binary Trees', 'BST', 'AVL Trees',
        'Graphs', 'DFS', 'BFS',
        'Hash Tables', 'Heaps', 'Tries'
      ],
      'Database Management': [
        'DBMS Architecture', 'Data Models', 'ER Diagrams',
        'Relational Algebra', 'SQL Queries', 'Joins',
        'Normalization', 'BCNF', 'Functional Dependencies',
        'Transactions', 'ACID Properties', 'Concurrency Control',
        'Indexing', 'Query Optimization', 'NoSQL'
      ],
      'Computer Networks': [
        'Network Topology', 'OSI Model', 'TCP/IP',
        'Ethernet', 'Switching', 'VLANs',
        'IP Addressing', 'Routing', 'OSPF',
        'TCP', 'UDP', 'Flow Control',
        'HTTP', 'DNS', 'Email Protocols'
      ]
    };

    return topicMap[subject] || [
      'Introduction', 'Basic Concepts', 'Fundamentals',
      'Core Theory', 'Practical Applications', 'Implementation',
      'Advanced Topics', 'Case Studies', 'Best Practices',
      'Current Trends', 'Future Directions', 'Industry Applications',
      'Research Areas', 'Emerging Technologies', 'Conclusion'
    ];
  }

  private generateTextBooks(subject: string) {
    const bookMap = {
      'Machine Learning': [
        {
          title: 'Pattern Recognition and Machine Learning',
          authors: ['Christopher M. Bishop'],
          publisher: 'Springer',
          year: 2006,
          isbn: '978-**********',
          edition: '1st'
        },
        {
          title: 'The Elements of Statistical Learning',
          authors: ['Trevor Hastie', 'Robert Tibshirani', 'Jerome Friedman'],
          publisher: 'Springer',
          year: 2009,
          isbn: '978-0387848570',
          edition: '2nd'
        },
        {
          title: 'Machine Learning: A Probabilistic Perspective',
          authors: ['Kevin P. Murphy'],
          publisher: 'MIT Press',
          year: 2012,
          isbn: '978-0262018029',
          edition: '1st'
        }
      ],
      'Data Structures': [
        {
          title: 'Introduction to Algorithms',
          authors: ['Thomas H. Cormen', 'Charles E. Leiserson', 'Ronald L. Rivest', 'Clifford Stein'],
          publisher: 'MIT Press',
          year: 2009,
          isbn: '978-0262033848',
          edition: '3rd'
        },
        {
          title: 'Data Structures and Algorithms in Java',
          authors: ['Robert Lafore'],
          publisher: 'Sams Publishing',
          year: 2017,
          isbn: '978-0672324536',
          edition: '2nd'
        },
        {
          title: 'Algorithms',
          authors: ['Robert Sedgewick', 'Kevin Wayne'],
          publisher: 'Addison-Wesley',
          year: 2011,
          isbn: '978-0321573513',
          edition: '4th'
        }
      ],
      'Database Management': [
        {
          title: 'Database System Concepts',
          authors: ['Abraham Silberschatz', 'Henry F. Korth', 'S. Sudarshan'],
          publisher: 'McGraw-Hill',
          year: 2019,
          isbn: '978-0078022159',
          edition: '7th'
        },
        {
          title: 'Fundamentals of Database Systems',
          authors: ['Ramez Elmasri', 'Shamkant B. Navathe'],
          publisher: 'Pearson',
          year: 2015,
          isbn: '978-0133970777',
          edition: '7th'
        },
        {
          title: 'Database Management Systems',
          authors: ['Raghu Ramakrishnan', 'Johannes Gehrke'],
          publisher: 'McGraw-Hill',
          year: 2002,
          isbn: '978-0072465631',
          edition: '3rd'
        }
      ],
      'Computer Networks': [
        {
          title: 'Computer Networking: A Top-Down Approach',
          authors: ['James F. Kurose', 'Keith W. Ross'],
          publisher: 'Pearson',
          year: 2020,
          isbn: '978-0135928615',
          edition: '8th'
        },
        {
          title: 'Computer Networks',
          authors: ['Andrew S. Tanenbaum', 'David J. Wetherall'],
          publisher: 'Pearson',
          year: 2010,
          isbn: '978-0132126953',
          edition: '5th'
        },
        {
          title: 'TCP/IP Illustrated, Volume 1',
          authors: ['W. Richard Stevens'],
          publisher: 'Addison-Wesley',
          year: 2011,
          isbn: '978-0321336316',
          edition: '2nd'
        }
      ],
      'Database Management Systems': [
        {
          title: 'Database System Concepts',
          authors: ['Abraham Silberschatz', 'Henry F. Korth', 'S. Sudarshan'],
          publisher: 'McGraw-Hill',
          year: 2019,
          isbn: '978-0078022159',
          edition: '7th'
        },
        {
          title: 'Fundamentals of Database Systems',
          authors: ['Ramez Elmasri', 'Shamkant B. Navathe'],
          publisher: 'Pearson',
          year: 2015,
          isbn: '978-0133970777',
          edition: '7th'
        },
        {
          title: 'Database Management Systems',
          authors: ['Raghu Ramakrishnan', 'Johannes Gehrke'],
          publisher: 'McGraw-Hill',
          year: 2002,
          isbn: '978-0072465631',
          edition: '3rd'
        }
      ],
      'Software Engineering': [
        {
          title: 'Software Engineering: A Practitioner\'s Approach',
          authors: ['Roger S. Pressman', 'Bruce R. Maxim'],
          publisher: 'McGraw-Hill',
          year: 2019,
          isbn: '978-1259872976',
          edition: '9th'
        },
        {
          title: 'Clean Code: A Handbook of Agile Software Craftsmanship',
          authors: ['Robert C. Martin'],
          publisher: 'Prentice Hall',
          year: 2008,
          isbn: '978-0132350884',
          edition: '1st'
        },
        {
          title: 'Design Patterns: Elements of Reusable Object-Oriented Software',
          authors: ['Erich Gamma', 'Richard Helm', 'Ralph Johnson', 'John Vlissides'],
          publisher: 'Addison-Wesley',
          year: 1994,
          isbn: '978-**********',
          edition: '1st'
        }
      ],
      'Web Development': [
        {
          title: 'JavaScript: The Definitive Guide',
          authors: ['David Flanagan'],
          publisher: 'O\'Reilly Media',
          year: 2020,
          isbn: '978-**********',
          edition: '7th'
        },
        {
          title: 'Learning React: Modern Patterns for Developing React Apps',
          authors: ['Alex Banks', 'Eve Porcello'],
          publisher: 'O\'Reilly Media',
          year: 2020,
          isbn: '978-**********',
          edition: '2nd'
        },
        {
          title: 'Node.js Design Patterns',
          authors: ['Mario Casciaro', 'Luciano Mammino'],
          publisher: 'Packt Publishing',
          year: 2020,
          isbn: '978-**********',
          edition: '3rd'
        }
      ],
      'Artificial Intelligence': [
        {
          title: 'Artificial Intelligence: A Modern Approach',
          authors: ['Stuart Russell', 'Peter Norvig'],
          publisher: 'Pearson',
          year: 2020,
          isbn: '978-**********',
          edition: '4th'
        },
        {
          title: 'Introduction to Artificial Intelligence',
          authors: ['Philip C. Jackson'],
          publisher: 'Dover Publications',
          year: 2019,
          isbn: '978-**********',
          edition: '2nd'
        },
        {
          title: 'AI: A Guide for Thinking Humans',
          authors: ['Melanie Mitchell'],
          publisher: 'Farrar, Straus and Giroux',
          year: 2019,
          isbn: '978-0374257835',
          edition: '1st'
        }
      ],
      'Computer Graphics': [
        {
          title: 'Computer Graphics: Principles and Practice',
          authors: ['John F. Hughes', 'Andries van Dam', 'Morgan McGuire'],
          publisher: 'Addison-Wesley',
          year: 2013,
          isbn: '978-0321399526',
          edition: '3rd'
        },
        {
          title: 'Real-Time Rendering',
          authors: ['Tomas Akenine-Möller', 'Eric Haines', 'Naty Hoffman'],
          publisher: 'A K Peters/CRC Press',
          year: 2018,
          isbn: '978-1138627000',
          edition: '4th'
        },
        {
          title: 'OpenGL Programming Guide',
          authors: ['Dave Shreiner', 'Graham Sellers', 'John Kessenich'],
          publisher: 'Addison-Wesley',
          year: 2016,
          isbn: '978-0134495491',
          edition: '9th'
        }
      ],
      'Cybersecurity': [
        {
          title: 'Network Security Essentials',
          authors: ['William Stallings'],
          publisher: 'Pearson',
          year: 2016,
          isbn: '978-0134527338',
          edition: '6th'
        },
        {
          title: 'The Web Application Hacker\'s Handbook',
          authors: ['Dafydd Stuttard', 'Marcus Pinto'],
          publisher: 'Wiley',
          year: 2011,
          isbn: '978-1118026472',
          edition: '2nd'
        },
        {
          title: 'Applied Cryptography',
          authors: ['Bruce Schneier'],
          publisher: 'Wiley',
          year: 2015,
          isbn: '978-1119096726',
          edition: '2nd'
        }
      ],
      'Mobile App Development': [
        {
          title: 'Android Programming: The Big Nerd Ranch Guide',
          authors: ['Bill Phillips', 'Chris Stewart', 'Kristin Marsicano'],
          publisher: 'Big Nerd Ranch Guides',
          year: 2019,
          isbn: '978-0135245125',
          edition: '4th'
        },
        {
          title: 'iOS Programming: The Big Nerd Ranch Guide',
          authors: ['Christian Keur', 'Aaron Hillegass'],
          publisher: 'Big Nerd Ranch Guides',
          year: 2020,
          isbn: '978-0135264027',
          edition: '7th'
        },
        {
          title: 'Flutter in Action',
          authors: ['Eric Windmill'],
          publisher: 'Manning Publications',
          year: 2019,
          isbn: '978-1617296147',
          edition: '1st'
        }
      ],
      'Cloud Computing': [
        {
          title: 'Amazon Web Services in Action',
          authors: ['Andreas Wittig', 'Michael Wittig'],
          publisher: 'Manning Publications',
          year: 2018,
          isbn: '978-1617295119',
          edition: '2nd'
        },
        {
          title: 'Kubernetes in Action',
          authors: ['Marko Lukša'],
          publisher: 'Manning Publications',
          year: 2017,
          isbn: '978-1617293726',
          edition: '1st'
        },
        {
          title: 'Docker Deep Dive',
          authors: ['Nigel Poulton'],
          publisher: 'Independently published',
          year: 2020,
          isbn: '978-1521822807',
          edition: '1st'
        }
      ],
      'Operating Systems': [
        {
          title: 'Operating System Concepts',
          authors: ['Abraham Silberschatz', 'Peter Baer Galvin', 'Greg Gagne'],
          publisher: 'Wiley',
          year: 2018,
          isbn: '978-1118063330',
          edition: '10th'
        },
        {
          title: 'Modern Operating Systems',
          authors: ['Andrew S. Tanenbaum', 'Herbert Bos'],
          publisher: 'Pearson',
          year: 2014,
          isbn: '978-0133591620',
          edition: '4th'
        },
        {
          title: 'Operating Systems: Three Easy Pieces',
          authors: ['Remzi H. Arpaci-Dusseau', 'Andrea C. Arpaci-Dusseau'],
          publisher: 'CreateSpace Independent Publishing',
          year: 2018,
          isbn: '978-1985086593',
          edition: '1st'
        }
      ]
    };

    const books = bookMap[subject];
    if (books && books.length >= 3) {
      return books.slice(0, 3); // Return exactly 3 textbooks
    }

    // Default textbooks if subject not found
    return [
      {
        title: `Fundamentals of ${subject}`,
        authors: ['Dr. Academic Author'],
        publisher: 'Technical Publications',
        year: 2023,
        isbn: '978-1234567890',
        edition: '1st'
      },
      {
        title: `Advanced ${subject}`,
        authors: ['Prof. Expert Writer'],
        publisher: 'Educational Press',
        year: 2022,
        isbn: '978-0987654321',
        edition: '2nd'
      },
      {
        title: `Comprehensive Guide to ${subject}`,
        authors: ['Dr. Subject Expert'],
        publisher: 'Academic Press',
        year: 2021,
        isbn: '978-1111111111',
        edition: '1st'
      }
    ];
  }

  private generateReferenceBooks(subject: string) {
    const referenceMap = {
      'Machine Learning': [
        {
          title: 'Hands-On Machine Learning with Scikit-Learn and TensorFlow',
          authors: ['Aurélien Géron'],
          publisher: 'O\'Reilly Media',
          year: 2019,
          isbn: '978-1492032649',
          edition: '2nd'
        },
        {
          title: 'Python Machine Learning',
          authors: ['Sebastian Raschka', 'Vahid Mirjalili'],
          publisher: 'Packt Publishing',
          year: 2019,
          isbn: '978-1789955750',
          edition: '3rd'
        },
        {
          title: 'Deep Learning',
          authors: ['Ian Goodfellow', 'Yoshua Bengio', 'Aaron Courville'],
          publisher: 'MIT Press',
          year: 2016,
          isbn: '978-0262035613',
          edition: '1st'
        }
      ],
      'Data Structures': [
        {
          title: 'Data Structures and Algorithm Analysis in C++',
          authors: ['Mark Allen Weiss'],
          publisher: 'Pearson',
          year: 2013,
          isbn: '978-0132847377',
          edition: '4th'
        },
        {
          title: 'Cracking the Coding Interview',
          authors: ['Gayle Laakmann McDowell'],
          publisher: 'CareerCup',
          year: 2015,
          isbn: '978-0984782857',
          edition: '6th'
        },
        {
          title: 'Algorithm Design Manual',
          authors: ['Steven S. Skiena'],
          publisher: 'Springer',
          year: 2020,
          isbn: '978-3030542559',
          edition: '3rd'
        }
      ],
      'Database Management': [
        {
          title: 'SQL in 10 Minutes, Sams Teach Yourself',
          authors: ['Ben Forta'],
          publisher: 'Sams Publishing',
          year: 2019,
          isbn: '978-**********',
          edition: '5th'
        },
        {
          title: 'Learning SQL',
          authors: ['Alan Beaulieu'],
          publisher: 'O\'Reilly Media',
          year: 2020,
          isbn: '978-**********',
          edition: '3rd'
        },
        {
          title: 'NoSQL Distilled',
          authors: ['Pramod J. Sadalage', 'Martin Fowler'],
          publisher: 'Addison-Wesley',
          year: 2012,
          isbn: '978-0321826626',
          edition: '1st'
        }
      ],
      'Computer Networks': [
        {
          title: 'Network+ Guide to Networks',
          authors: ['Tamara Dean'],
          publisher: 'Cengage Learning',
          year: 2019,
          isbn: '978-**********',
          edition: '8th'
        },
        {
          title: 'Wireshark Network Analysis',
          authors: ['Laura Chappell'],
          publisher: 'Chappell University',
          year: 2017,
          isbn: '978-**********',
          edition: '2nd'
        },
        {
          title: 'CCNA Routing and Switching Complete Study Guide',
          authors: ['Todd Lammle'],
          publisher: 'Sybex',
          year: 2016,
          isbn: '978-**********',
          edition: '2nd'
        }
      ],
      'Database Management Systems': [
        {
          title: 'SQL in 10 Minutes, Sams Teach Yourself',
          authors: ['Ben Forta'],
          publisher: 'Sams Publishing',
          year: 2019,
          isbn: '978-**********',
          edition: '5th'
        },
        {
          title: 'Learning SQL',
          authors: ['Alan Beaulieu'],
          publisher: 'O\'Reilly Media',
          year: 2020,
          isbn: '978-**********',
          edition: '3rd'
        },
        {
          title: 'High Performance MySQL',
          authors: ['Baron Schwartz', 'Peter Zaitsev', 'Vadim Tkachenko'],
          publisher: 'O\'Reilly Media',
          year: 2012,
          isbn: '978-**********',
          edition: '3rd'
        }
      ],
      'Software Engineering': [
        {
          title: 'The Pragmatic Programmer',
          authors: ['David Thomas', 'Andrew Hunt'],
          publisher: 'Addison-Wesley',
          year: 2019,
          isbn: '978-0135957059',
          edition: '2nd'
        },
        {
          title: 'Code Complete',
          authors: ['Steve McConnell'],
          publisher: 'Microsoft Press',
          year: 2004,
          isbn: '978-0735619678',
          edition: '2nd'
        },
        {
          title: 'Refactoring: Improving the Design of Existing Code',
          authors: ['Martin Fowler'],
          publisher: 'Addison-Wesley',
          year: 2018,
          isbn: '978-0134757599',
          edition: '2nd'
        }
      ],
      'Web Development': [
        {
          title: 'Eloquent JavaScript',
          authors: ['Marijn Haverbeke'],
          publisher: 'No Starch Press',
          year: 2018,
          isbn: '978-1593279509',
          edition: '3rd'
        },
        {
          title: 'You Don\'t Know JS',
          authors: ['Kyle Simpson'],
          publisher: 'O\'Reilly Media',
          year: 2015,
          isbn: '978-1491924464',
          edition: '1st'
        },
        {
          title: 'Full Stack React',
          authors: ['Anthony Accomazzo', 'Nate Murray', 'Ari Lerner'],
          publisher: 'Fullstack.io',
          year: 2017,
          isbn: '978-0991344628',
          edition: '1st'
        }
      ],
      'Artificial Intelligence': [
        {
          title: 'Machine Learning Yearning',
          authors: ['Andrew Ng'],
          publisher: 'Self-published',
          year: 2018,
          isbn: '978-0999579909',
          edition: '1st'
        },
        {
          title: 'Hands-On Machine Learning',
          authors: ['Aurélien Géron'],
          publisher: 'O\'Reilly Media',
          year: 2019,
          isbn: '978-1492032649',
          edition: '2nd'
        },
        {
          title: 'The Hundred-Page Machine Learning Book',
          authors: ['Andriy Burkov'],
          publisher: 'Self-published',
          year: 2019,
          isbn: '978-1999579500',
          edition: '1st'
        }
      ],
      'Computer Graphics': [
        {
          title: 'OpenGL SuperBible',
          authors: ['Graham Sellers', 'Richard S. Wright Jr.', 'Nicholas Haemel'],
          publisher: 'Addison-Wesley',
          year: 2015,
          isbn: '978-0672337475',
          edition: '7th'
        },
        {
          title: 'Game Engine Architecture',
          authors: ['Jason Gregory'],
          publisher: 'A K Peters/CRC Press',
          year: 2018,
          isbn: '978-1138035454',
          edition: '3rd'
        },
        {
          title: 'Physically Based Rendering',
          authors: ['Matt Pharr', 'Wenzel Jakob', 'Greg Humphreys'],
          publisher: 'Morgan Kaufmann',
          year: 2016,
          isbn: '978-0128006450',
          edition: '3rd'
        }
      ],
      'Cybersecurity': [
        {
          title: 'Metasploit: The Penetration Tester\'s Guide',
          authors: ['David Kennedy', 'Jim O\'Gorman', 'Devon Kearns'],
          publisher: 'No Starch Press',
          year: 2011,
          isbn: '978-1593272883',
          edition: '1st'
        },
        {
          title: 'The Hacker Playbook 3',
          authors: ['Peter Kim'],
          publisher: 'Secure Planet LLC',
          year: 2018,
          isbn: '978-1980901761',
          edition: '3rd'
        },
        {
          title: 'Cryptography Engineering',
          authors: ['Niels Ferguson', 'Bruce Schneier', 'Tadayoshi Kohno'],
          publisher: 'Wiley',
          year: 2010,
          isbn: '978-0470474242',
          edition: '1st'
        }
      ],
      'Mobile App Development': [
        {
          title: 'Head First Android Development',
          authors: ['Dawn Griffiths', 'David Griffiths'],
          publisher: 'O\'Reilly Media',
          year: 2017,
          isbn: '978-1491974049',
          edition: '2nd'
        },
        {
          title: 'Swift Programming: The Big Nerd Ranch Guide',
          authors: ['Matthew Mathias', 'John Gallagher'],
          publisher: 'Big Nerd Ranch Guides',
          year: 2016,
          isbn: '978-0134610610',
          edition: '2nd'
        },
        {
          title: 'React Native in Action',
          authors: ['Nader Dabit'],
          publisher: 'Manning Publications',
          year: 2019,
          isbn: '978-1617294051',
          edition: '1st'
        }
      ],
      'Cloud Computing': [
        {
          title: 'AWS Certified Solutions Architect Study Guide',
          authors: ['Ben Piper', 'David Clinton'],
          publisher: 'Sybex',
          year: 2020,
          isbn: '978-1119713081',
          edition: '3rd'
        },
        {
          title: 'Kubernetes: Up and Running',
          authors: ['Kelsey Hightower', 'Brendan Burns', 'Joe Beda'],
          publisher: 'O\'Reilly Media',
          year: 2019,
          isbn: '978-1492046530',
          edition: '2nd'
        },
        {
          title: 'Docker in Practice',
          authors: ['Ian Miell', 'Aidan Hobson Sayers'],
          publisher: 'Manning Publications',
          year: 2019,
          isbn: '978-1617294808',
          edition: '2nd'
        }
      ],
      'Operating Systems': [
        {
          title: 'The Linux Programming Interface',
          authors: ['Michael Kerrisk'],
          publisher: 'No Starch Press',
          year: 2010,
          isbn: '978-1593272203',
          edition: '1st'
        },
        {
          title: 'Understanding the Linux Kernel',
          authors: ['Daniel P. Bovet', 'Marco Cesati'],
          publisher: 'O\'Reilly Media',
          year: 2005,
          isbn: '978-0596005658',
          edition: '3rd'
        },
        {
          title: 'Windows Internals',
          authors: ['Pavel Yosifovich', 'Alex Ionescu', 'Mark E. Russinovich'],
          publisher: 'Microsoft Press',
          year: 2017,
          isbn: '978-0735684188',
          edition: '7th'
        }
      ]
    };

    const books = referenceMap[subject];
    if (books && books.length >= 3) {
      return books.slice(0, 3); // Return exactly 3 reference books
    }

    // Default reference books if subject not found
    return [
      {
        title: `${subject} Reference Manual`,
        authors: ['Reference Author'],
        publisher: 'Reference Publications',
        year: 2023,
        isbn: '978-2222222222',
        edition: '1st'
      },
      {
        title: `Practical ${subject}`,
        authors: ['Practical Expert'],
        publisher: 'Practical Press',
        year: 2022,
        isbn: '978-3333333333',
        edition: '1st'
      },
      {
        title: `${subject} Handbook`,
        authors: ['Handbook Author'],
        publisher: 'Handbook Publications',
        year: 2021,
        isbn: '978-4444444444',
        edition: '1st'
      }
    ];
  }

  async generateCourseWithResearch(request: CourseGenerationRequest, userPreferences?: { cosCount: number; unitsCount: number }): Promise<Course> {
    console.log('🔍 Enhanced Mock Service: Researching syllabus content from top institutes...');
    console.log('📋 Subject:', request.subject);

    // Use the same syllabus research logic as LangChain service
    const syllabusData = this.researchSyllabus(request.subject);
    console.log('📚 Syllabus research completed:', syllabusData.confidence);
    console.log('🏛️ Source:', syllabusData.source);

    if (syllabusData.confidence === 'high') {
      console.log('✅ Using detailed IIT/IISc syllabus content');
      return this.buildCourseFromResearch(request, syllabusData, userPreferences);
    } else {
      console.log('⚠️ Using enhanced fallback content');
      return this.generateCourse(request, userPreferences);
    }
  }

  private getSyllabusDatabase() {
    return {
      'machine learning': {
        units: [
          {
            title: 'Mathematical Foundations and Introduction',
            topics: [
              'Linear Algebra: Vectors, Matrices, Eigenvalues, SVD',
              'Probability Theory: Bayes Theorem, Distributions',
              'Statistics: Hypothesis Testing, Confidence Intervals',
              'Optimization: Gradient Descent, Lagrange Multipliers',
              'Information Theory: Entropy, Mutual Information'
            ]
          },
          {
            title: 'Supervised Learning Algorithms',
            topics: [
              'Linear Regression: OLS, Ridge, Lasso Regression',
              'Logistic Regression: Binary and Multiclass Classification',
              'Decision Trees: ID3, C4.5, CART, Pruning Techniques',
              'Support Vector Machines: Linear and Non-linear SVM, Kernel Trick',
              'Naive Bayes: Gaussian, Multinomial, Bernoulli NB'
            ]
          },
          {
            title: 'Unsupervised Learning and Dimensionality Reduction',
            topics: [
              'K-Means Clustering: Lloyd\'s Algorithm, K-means++',
              'Hierarchical Clustering: Agglomerative, Divisive Methods',
              'DBSCAN: Density-based Clustering, Parameter Selection',
              'Principal Component Analysis: Eigenvalue Decomposition',
              'Independent Component Analysis: FastICA Algorithm'
            ]
          },
          {
            title: 'Neural Networks and Deep Learning',
            topics: [
              'Perceptron: Single and Multi-layer Perceptrons',
              'Backpropagation: Chain Rule, Gradient Computation',
              'Convolutional Neural Networks: Conv Layers, Pooling',
              'Recurrent Neural Networks: LSTM, GRU, Attention',
              'Regularization: Dropout, Batch Normalization, Weight Decay'
            ]
          },
          {
            title: 'Advanced Topics and Applications',
            topics: [
              'Ensemble Methods: Bagging, Boosting, Random Forest',
              'Reinforcement Learning: Q-Learning, Policy Gradients',
              'Natural Language Processing: Word Embeddings, Transformers',
              'Computer Vision: Object Detection, Image Segmentation',
              'Model Evaluation: Cross-validation, ROC, Precision-Recall'
            ]
          }
        ],
        textbooks: [
          'Pattern Recognition and Machine Learning - Christopher Bishop',
          'The Elements of Statistical Learning - Hastie, Tibshirani, Friedman',
          'Machine Learning: A Probabilistic Perspective - Kevin Murphy'
        ],
        references: [
          'Hands-On Machine Learning - Aurélien Géron',
          'Deep Learning - Ian Goodfellow, Yoshua Bengio, Aaron Courville',
          'Python Machine Learning - Sebastian Raschka'
        ]
      },
      'operating systems': {
        units: [
          {
            title: 'Introduction to Operating Systems',
            topics: [
              'OS Structure: Monolithic, Microkernel, Hybrid Architectures',
              'System Calls: Process, File, Device, Information Maintenance',
              'OS Services: Program Execution, I/O Operations, File Systems',
              'User and Kernel Modes: Privilege Levels, Mode Switching',
              'Interrupts and Exceptions: Hardware and Software Interrupts'
            ]
          },
          {
            title: 'Process Management',
            topics: [
              'Process Concept: PCB, Process States, Process Creation',
              'CPU Scheduling: FCFS, SJF, Priority, Round Robin',
              'Process Synchronization: Critical Section, Semaphores',
              'Deadlocks: Detection, Prevention, Avoidance, Recovery',
              'Inter-process Communication: Pipes, Message Queues, Shared Memory'
            ]
          },
          {
            title: 'Memory Management',
            topics: [
              'Memory Hierarchy: Cache, Main Memory, Secondary Storage',
              'Contiguous Allocation: Fixed and Variable Partitioning',
              'Paging: Page Tables, TLB, Multi-level Paging',
              'Virtual Memory: Demand Paging, Page Replacement Algorithms',
              'Segmentation: Segment Tables, Segmentation with Paging'
            ]
          },
          {
            title: 'File Systems and I/O',
            topics: [
              'File System Interface: File Operations, Directory Structure',
              'File System Implementation: Allocation Methods, Free Space',
              'Mass Storage: Disk Scheduling, RAID, SSD Technology',
              'I/O Systems: Hardware, Application Interface, Kernel I/O',
              'Protection and Security: Access Control, Authentication'
            ]
          },
          {
            title: 'Advanced Topics',
            topics: [
              'Distributed Systems: Network OS, Distributed File Systems',
              'Real-time Systems: Hard and Soft Real-time, Scheduling',
              'Virtualization: Hypervisors, Container Technology',
              'Mobile Operating Systems: Android, iOS Architecture',
              'Security: Buffer Overflow, Access Control, Cryptography'
            ]
          },
          {
            title: 'System Programming and Kernel Development',
            topics: [
              'System Calls Implementation: Kernel Mode Transitions',
              'Device Drivers: Character and Block Device Drivers',
              'Kernel Modules: Loading, Unloading, Module Parameters',
              'Interrupt Handling: Top Half, Bottom Half, Tasklets',
              'Kernel Synchronization: Spinlocks, Mutexes, RCU'
            ]
          },
          {
            title: 'Performance and Optimization',
            topics: [
              'System Performance Monitoring: CPU, Memory, I/O Metrics',
              'Profiling Tools: perf, gprof, Valgrind, SystemTap',
              'Memory Optimization: Cache-friendly Programming, NUMA',
              'I/O Optimization: Asynchronous I/O, Direct I/O, Memory Mapping',
              'Scheduling Optimization: CFS, RT Scheduling, Load Balancing'
            ]
          },
          {
            title: 'Modern Operating System Concepts',
            topics: [
              'Container Technology: Docker, LXC, Namespaces, Cgroups',
              'Microkernel Architecture: L4, QNX, Minix Design Principles',
              'Unikernel Systems: Library OS, Application-specific Kernels',
              'Cloud Operating Systems: Hypervisor Types, VM Management',
              'Embedded OS: Real-time Constraints, Resource Management'
            ]
          }
        ],
        textbooks: [
          'Operating System Concepts - Silberschatz, Galvin, Gagne',
          'Modern Operating Systems - Andrew Tanenbaum',
          'Operating Systems: Three Easy Pieces - Remzi Arpaci-Dusseau'
        ],
        references: [
          'The Design and Implementation of the FreeBSD Operating System',
          'Linux Kernel Development - Robert Love',
          'Understanding the Linux Kernel - Daniel Bovet'
        ]
      },
      'computer networks': {
        units: [
          {
            title: 'Network Fundamentals and Architecture',
            topics: [
              'Network Types: LAN, WAN, MAN, PAN, Network Topologies',
              'OSI Reference Model: Seven Layers, Protocol Stack',
              'TCP/IP Model: Application, Transport, Internet, Link Layers',
              'Network Performance: Bandwidth, Latency, Throughput, Jitter',
              'Network Standards: IEEE 802, ITU-T, IETF Standards'
            ]
          },
          {
            title: 'Physical and Data Link Layer',
            topics: [
              'Transmission Media: Guided and Unguided Media',
              'Digital Transmission: Line Coding, Block Coding',
              'Error Detection and Correction: Parity, CRC, Hamming Code',
              'Data Link Protocols: HDLC, PPP, Ethernet',
              'Multiple Access: ALOHA, CSMA/CD, Token Ring'
            ]
          },
          {
            title: 'Network Layer and Routing',
            topics: [
              'IP Addressing: IPv4, IPv6, Subnetting, CIDR',
              'Routing Algorithms: Distance Vector, Link State',
              'Routing Protocols: RIP, OSPF, BGP',
              'Network Address Translation: NAT, PAT',
              'Internet Control Protocols: ICMP, ARP, DHCP'
            ]
          },
          {
            title: 'Transport Layer Protocols',
            topics: [
              'UDP: Connectionless Service, UDP Header Format',
              'TCP: Connection-oriented Service, Three-way Handshake',
              'Flow Control: Stop-and-wait, Sliding Window',
              'Congestion Control: Slow Start, Congestion Avoidance',
              'Quality of Service: Traffic Shaping, Packet Scheduling'
            ]
          },
          {
            title: 'Application Layer and Network Security',
            topics: [
              'DNS: Domain Name System, DNS Resolution Process',
              'HTTP/HTTPS: Web Protocols, REST APIs',
              'Email Protocols: SMTP, POP3, IMAP',
              'Network Security: Cryptography, Firewalls, VPN',
              'Wireless Networks: WiFi, Bluetooth, Cellular Networks'
            ]
          }
        ],
        textbooks: [
          'Computer Networking: A Top-Down Approach - Kurose, Ross',
          'Computer Networks - Andrew Tanenbaum',
          'TCP/IP Illustrated - W. Richard Stevens'
        ],
        references: [
          'Network+ Guide to Networks - Tamara Dean',
          'Internetworking with TCP/IP - Douglas Comer',
          'Computer Networks and Internets - Douglas Comer'
        ]
      },
      'data structures': {
        units: [
          {
            title: 'Introduction and Array-based Structures',
            topics: [
              'Abstract Data Types: Interface vs Implementation',
              'Algorithm Analysis: Big O, Omega, Theta Notation',
              'Arrays: Static and Dynamic Arrays, Multi-dimensional Arrays',
              'Strings: String Matching Algorithms, KMP, Boyer-Moore',
              'Sparse Matrices: Representation and Operations'
            ]
          },
          {
            title: 'Linear Data Structures',
            topics: [
              'Linked Lists: Singly, Doubly, Circular Linked Lists',
              'Stacks: Array and Linked Implementation, Applications',
              'Queues: Linear, Circular, Priority Queues, Deque',
              'Expression Evaluation: Infix, Prefix, Postfix Conversion',
              'Recursion: Direct, Indirect, Tail Recursion, Stack Frames'
            ]
          },
          {
            title: 'Trees and Hierarchical Structures',
            topics: [
              'Binary Trees: Traversals, Threaded Binary Trees',
              'Binary Search Trees: Insertion, Deletion, Search Operations',
              'AVL Trees: Rotation Operations, Height Balancing',
              'B-Trees and B+ Trees: Multi-way Search Trees',
              'Heap Data Structure: Min-Heap, Max-Heap, Heap Sort'
            ]
          },
          {
            title: 'Graphs and Advanced Structures',
            topics: [
              'Graph Representation: Adjacency Matrix, Adjacency List',
              'Graph Traversals: Depth-First Search, Breadth-First Search',
              'Shortest Path Algorithms: Dijkstra, Bellman-Ford, Floyd-Warshall',
              'Minimum Spanning Tree: Kruskal, Prim Algorithms',
              'Topological Sorting: Kahn Algorithm, DFS-based Approach'
            ]
          },
          {
            title: 'Hashing and Advanced Topics',
            topics: [
              'Hash Tables: Hash Functions, Collision Resolution',
              'Open Addressing: Linear, Quadratic, Double Hashing',
              'Separate Chaining: Implementation and Analysis',
              'Tries: Prefix Trees, Compressed Tries, Applications',
              'Disjoint Set Union: Union-Find, Path Compression'
            ]
          }
        ],
        textbooks: [
          'Introduction to Algorithms - Cormen, Leiserson, Rivest, Stein',
          'Data Structures and Algorithms in Java - Robert Lafore',
          'Algorithms - Robert Sedgewick, Kevin Wayne'
        ],
        references: [
          'Data Structures and Algorithm Analysis in C++ - Mark Allen Weiss',
          'Cracking the Coding Interview - Gayle Laakmann McDowell',
          'Algorithm Design Manual - Steven Skiena'
        ]
      },
      // Civil Engineering
      'structural engineering': {
        units: [
          {
            title: 'Fundamentals of Structural Analysis',
            topics: [
              'Structural Systems: Determinate and Indeterminate Structures',
              'Force Method: Flexibility Matrix, Compatibility Equations',
              'Displacement Method: Stiffness Matrix, Equilibrium Equations',
              'Matrix Methods: Direct Stiffness Method, Global Assembly',
              'Influence Lines: Moving Loads, Maximum Response'
            ]
          },
          {
            title: 'Advanced Structural Analysis',
            topics: [
              'Moment Distribution Method: Fixed End Moments, Distribution Factors',
              'Slope Deflection Method: Joint Rotations, Member End Moments',
              'Plastic Analysis: Yield Line Theory, Limit Load Analysis',
              'Stability Analysis: Buckling, Critical Load, Effective Length',
              'Dynamic Analysis: Natural Frequency, Mode Shapes, Response Spectrum'
            ]
          },
          {
            title: 'Design of Steel Structures',
            topics: [
              'Steel Properties: Yield Strength, Ultimate Strength, Ductility',
              'Tension Members: Net Section, Block Shear, Connection Design',
              'Compression Members: Column Design, Buckling, Slenderness Ratio',
              'Flexural Members: Beam Design, Lateral Torsional Buckling',
              'Connections: Bolted, Welded, Moment Connections'
            ]
          },
          {
            title: 'Design of Concrete Structures',
            topics: [
              'Concrete Properties: Compressive Strength, Modulus of Elasticity',
              'Reinforced Concrete: Working Stress Method, Limit State Method',
              'Flexural Design: Singly Reinforced, Doubly Reinforced Beams',
              'Shear Design: Stirrups, Bent-up Bars, Critical Sections',
              'Column Design: Short Columns, Long Columns, Biaxial Bending'
            ]
          },
          {
            title: 'Advanced Structural Systems',
            topics: [
              'Prestressed Concrete: Pre-tensioning, Post-tensioning, Losses',
              'Composite Construction: Steel-Concrete Composite, Shear Connectors',
              'High-rise Buildings: Lateral Load Systems, Shear Walls, Outriggers',
              'Seismic Design: Response Spectrum, Base Isolation, Damping',
              'Bridge Engineering: Girder Bridges, Cable-stayed, Suspension Bridges'
            ]
          }
        ],
        textbooks: [
          'Structural Analysis - R.C. Hibbeler',
          'Design of Steel Structures - S.K. Duggal',
          'Reinforced Concrete Design - S.N. Sinha'
        ],
        references: [
          'Matrix Analysis of Structures - Aslam Kassimali',
          'Steel Structures - Salmon, Johnson, Malhas',
          'Reinforced Concrete - S.K. Mallick'
        ]
      },
      // Mechanical Engineering
      'thermodynamics': {
        units: [
          {
            title: 'Basic Concepts and First Law',
            topics: [
              'Thermodynamic Systems: Closed, Open, Isolated Systems',
              'Properties and State: Intensive, Extensive, Equilibrium',
              'Processes: Reversible, Irreversible, Quasi-static',
              'First Law: Internal Energy, Enthalpy, Heat, Work',
              'Applications: Constant Volume, Constant Pressure Processes'
            ]
          },
          {
            title: 'Second Law and Entropy',
            topics: [
              'Heat Engines: Carnot Cycle, Efficiency, COP',
              'Entropy: Definition, Clausius Inequality, Entropy Generation',
              'Availability: Reversible Work, Irreversibility, Second Law Efficiency',
              'Thermodynamic Relations: Maxwell Relations, Clapeyron Equation',
              'Property Relations: Specific Heats, Joule-Thomson Coefficient'
            ]
          },
          {
            title: 'Power and Refrigeration Cycles',
            topics: [
              'Gas Power Cycles: Otto, Diesel, Brayton, Stirling Cycles',
              'Vapor Power Cycles: Rankine Cycle, Reheat, Regeneration',
              'Refrigeration Cycles: Vapor Compression, Absorption, Gas Cycles',
              'Combined Cycles: Gas-Steam Combined, Cogeneration',
              'Cycle Analysis: Performance Parameters, Optimization'
            ]
          },
          {
            title: 'Psychrometrics and Combustion',
            topics: [
              'Psychrometric Properties: Humidity Ratio, Enthalpy, Wet Bulb Temperature',
              'Air Conditioning Processes: Heating, Cooling, Humidification',
              'Combustion: Stoichiometry, Air-Fuel Ratio, Adiabatic Flame Temperature',
              'Combustion Analysis: Orsat Analysis, Emission Control',
              'Alternative Fuels: Biofuels, Hydrogen, Fuel Cells'
            ]
          },
          {
            title: 'Advanced Thermodynamics',
            topics: [
              'Statistical Thermodynamics: Molecular Theory, Partition Functions',
              'Chemical Thermodynamics: Chemical Potential, Phase Equilibrium',
              'Non-equilibrium Thermodynamics: Irreversible Processes',
              'Computational Methods: Property Estimation, Cycle Simulation',
              'Environmental Impact: Sustainability, Life Cycle Assessment'
            ]
          }
        ],
        textbooks: [
          'Thermodynamics: An Engineering Approach - Cengel & Boles',
          'Engineering Thermodynamics - P.K. Nag',
          'Fundamentals of Thermodynamics - Borgnakke & Sonntag'
        ],
        references: [
          'Thermodynamics - Wark & Richards',
          'Introduction to Thermodynamics - Smith, Van Ness, Abbott',
          'Applied Thermodynamics - Eastop & McConkey'
        ]
      },
      'quantum mechanics': {
        units: [
          {
            title: 'Mathematical Foundations and Wave-Particle Duality',
            topics: [
              'Schrödinger Equation: Time-dependent and Time-independent Forms',
              'Wave Functions: Normalization, Probability Density, Expectation Values',
              'Operators: Hermitian Operators, Eigenvalue Problems, Commutation Relations',
              'Uncertainty Principle: Heisenberg Uncertainty, Energy-Time Uncertainty',
              'Wave-Particle Duality: de Broglie Wavelength, Double-slit Experiment'
            ]
          },
          {
            title: 'One-Dimensional Quantum Systems',
            topics: [
              'Particle in a Box: Infinite Square Well, Energy Levels, Wave Functions',
              'Harmonic Oscillator: Classical vs Quantum, Ladder Operators, Energy Eigenvalues',
              'Potential Barriers: Tunneling Effect, Transmission and Reflection Coefficients',
              'Delta Function Potential: Bound States, Scattering States',
              'Finite Square Well: Bound States, Transcendental Equations'
            ]
          },
          {
            title: 'Three-Dimensional Systems and Angular Momentum',
            topics: [
              'Hydrogen Atom: Radial and Angular Solutions, Quantum Numbers',
              'Spherical Harmonics: Orbital Angular Momentum, Magnetic Quantum Numbers',
              'Spin Angular Momentum: Pauli Matrices, Spin-1/2 Particles',
              'Addition of Angular Momenta: Clebsch-Gordan Coefficients, j-j Coupling',
              'Zeeman Effect: Weak and Strong Field Cases, Anomalous Zeeman Effect'
            ]
          },
          {
            title: 'Approximation Methods and Perturbation Theory',
            topics: [
              'Time-Independent Perturbation Theory: First and Second Order Corrections',
              'Degenerate Perturbation Theory: Lifting of Degeneracy, Good Quantum Numbers',
              'Variational Method: Trial Wave Functions, Helium Atom Ground State',
              'WKB Approximation: Semiclassical Approximation, Turning Points',
              'Time-Dependent Perturbation Theory: Transition Probabilities, Fermi\'s Golden Rule'
            ]
          },
          {
            title: 'Many-Particle Systems and Advanced Topics',
            topics: [
              'Identical Particles: Symmetrization Postulate, Fermions and Bosons',
              'Pauli Exclusion Principle: Electronic Structure of Atoms, Periodic Table',
              'Second Quantization: Creation and Annihilation Operators, Fock Space',
              'Quantum Entanglement: Bell\'s Theorem, EPR Paradox, Quantum Information',
              'Relativistic Quantum Mechanics: Klein-Gordon Equation, Dirac Equation'
            ]
          }
        ],
        textbooks: [
          'Introduction to Quantum Mechanics - David J. Griffiths',
          'Quantum Mechanics - Claude Cohen-Tannoudji',
          'Modern Quantum Mechanics - J.J. Sakurai'
        ],
        references: [
          'Principles of Quantum Mechanics - R. Shankar',
          'Quantum Mechanics - Leonard Schiff',
          'Advanced Quantum Mechanics - Franz Schwabl'
        ]
      },
      'linear algebra': {
        units: [
          {
            title: 'Vector Spaces and Linear Transformations',
            topics: [
              'Vector Spaces: Definition, Subspaces, Linear Independence, Basis and Dimension',
              'Linear Transformations: Kernel, Image, Rank-Nullity Theorem',
              'Matrix Representation: Change of Basis, Similar Matrices',
              'Inner Product Spaces: Gram-Schmidt Process, Orthogonal Projections',
              'Dual Spaces: Linear Functionals, Dual Basis, Double Dual'
            ]
          },
          {
            title: 'Eigenvalues and Diagonalization',
            topics: [
              'Eigenvalues and Eigenvectors: Characteristic Polynomial, Algebraic and Geometric Multiplicity',
              'Diagonalization: Diagonalizable Matrices, Jordan Normal Form',
              'Spectral Theorem: Symmetric Matrices, Orthogonal Diagonalization',
              'Quadratic Forms: Positive Definite Matrices, Principal Axis Theorem',
              'Singular Value Decomposition: SVD, Pseudoinverse, Low-rank Approximation'
            ]
          },
          {
            title: 'Advanced Matrix Theory',
            topics: [
              'Matrix Norms: Induced Norms, Frobenius Norm, Condition Numbers',
              'Matrix Functions: Matrix Exponential, Logarithm, Trigonometric Functions',
              'Perturbation Theory: Eigenvalue Sensitivity, Matrix Perturbations',
              'Generalized Eigenvalue Problems: Pencils, Kronecker Form',
              'Tensor Products: Kronecker Products, Vectorization, Matrix Equations'
            ]
          },
          {
            title: 'Numerical Linear Algebra',
            topics: [
              'LU Decomposition: Gaussian Elimination, Pivoting Strategies',
              'QR Decomposition: Householder Reflections, Givens Rotations',
              'Iterative Methods: Jacobi, Gauss-Seidel, Conjugate Gradient',
              'Eigenvalue Algorithms: Power Method, QR Algorithm, Lanczos Method',
              'Least Squares Problems: Normal Equations, QR Method, SVD Method'
            ]
          },
          {
            title: 'Applications and Advanced Topics',
            topics: [
              'Graph Theory Applications: Adjacency Matrices, Laplacian Matrices, Spectral Graph Theory',
              'Optimization: Linear Programming, Convex Optimization, Semidefinite Programming',
              'Machine Learning Applications: PCA, Kernel Methods, Matrix Factorization',
              'Control Theory: Controllability, Observability, Linear Quadratic Regulator',
              'Quantum Computing: Quantum Gates, Unitary Matrices, Quantum Algorithms'
            ]
          }
        ],
        textbooks: [
          'Linear Algebra Done Right - Sheldon Axler',
          'Introduction to Linear Algebra - Gilbert Strang',
          'Matrix Analysis - Roger Horn and Charles Johnson'
        ],
        references: [
          'Linear Algebra and Its Applications - David Lay',
          'Numerical Linear Algebra - Lloyd Trefethen',
          'Matrix Computations - Gene Golub and Charles Van Loan'
        ]
      },
      'physics': {
        units: [
          {
            title: 'Classical Mechanics',
            topics: [
              'Lagrangian Mechanics: Generalized Coordinates, Euler-Lagrange Equations',
              'Hamiltonian Mechanics: Canonical Transformations, Poisson Brackets',
              'Central Force Problems: Kepler Problem, Scattering Cross-sections',
              'Rigid Body Dynamics: Euler Angles, Moment of Inertia Tensor',
              'Small Oscillations: Normal Modes, Coupled Oscillators'
            ]
          },
          {
            title: 'Electromagnetic Theory',
            topics: [
              'Maxwell\'s Equations: Differential and Integral Forms, Boundary Conditions',
              'Electromagnetic Waves: Plane Waves, Polarization, Energy and Momentum',
              'Electrostatics: Multipole Expansion, Method of Images, Green\'s Functions',
              'Magnetostatics: Vector Potential, Magnetic Dipoles, Ampère\'s Law',
              'Electromagnetic Radiation: Retarded Potentials, Larmor Formula, Synchrotron Radiation'
            ]
          }
        ],
        textbooks: [
          'Classical Mechanics - Herbert Goldstein',
          'Introduction to Electrodynamics - David J. Griffiths'
        ],
        references: [
          'Mechanics - L.D. Landau and E.M. Lifshitz',
          'Classical Electrodynamics - J.D. Jackson'
        ]
      }
    };
  }

  private researchSyllabus(subject: string) {
    const normalizedSubject = subject.toLowerCase().replace(/[^a-z\s]/g, '').trim();
    console.log('🔍 Normalized subject:', normalizedSubject);

    const syllabusDatabase = this.getSyllabusDatabase();
    console.log('📚 Available subjects:', Object.keys(syllabusDatabase));

    const syllabusData = (syllabusDatabase as any)[normalizedSubject];

    if (syllabusData) {
      console.log('✅ Found detailed syllabus for:', normalizedSubject);
      return {
        subject: subject,
        source: 'Compiled from IIT Delhi, IIT Bombay, IIT Madras, IISc Bangalore syllabi',
        units: syllabusData.units,
        recommendedTextbooks: syllabusData.textbooks,
        referenceBooks: syllabusData.references,
        confidence: 'high'
      };
    } else {
      console.log('⚠️ No detailed syllabus found for:', normalizedSubject, '- using research framework');
      return {
        subject: subject,
        source: 'General academic framework',
        confidence: 'low',
        fallbackUnits: [
          {
            title: `Fundamentals of ${subject}`,
            topics: [`Introduction to ${subject}`, 'Historical Development', 'Basic Principles', 'Core Concepts', 'Applications']
          },
          {
            title: `Theoretical Foundations`,
            topics: ['Mathematical Models', 'Theoretical Framework', 'Key Algorithms', 'Design Principles', 'Analysis Methods']
          },
          {
            title: `Practical Applications`,
            topics: ['Implementation Techniques', 'Case Studies', 'Industry Applications', 'Tools and Technologies', 'Best Practices']
          },
          {
            title: `Advanced Topics`,
            topics: ['Recent Developments', 'Research Areas', 'Emerging Trends', 'Future Directions', 'Specialized Applications']
          },
          {
            title: `Project and Assessment`,
            topics: ['Project Work', 'Laboratory Exercises', 'Performance Evaluation', 'Quality Metrics', 'Professional Skills']
          }
        ]
      };
    }
  }

  private buildCourseFromResearch(request: CourseGenerationRequest, syllabusData: any, userPreferences?: { cosCount: number; unitsCount: number }): Course {
    const cosCount = userPreferences?.cosCount || 5;
    const unitsCount = userPreferences?.unitsCount || 5;

    // Use researched content
    const units = syllabusData.units || syllabusData.fallbackUnits || [];
    const selectedUnits = units.slice(0, unitsCount);

    // Build comprehensive units
    const courseUnits = selectedUnits.map((unit: any, index: number) => ({
      unitNumber: index + 1,
      title: unit.title,
      topics: unit.topics || [],
      topicDescriptions: {},
      learningOutcomes: [
        `Understand the fundamental concepts of ${unit.title}`,
        `Apply the principles and techniques learned in practical scenarios`,
        `Analyze and evaluate different approaches within this unit's scope`
      ],
      contactHours: 15,
      practicalHours: 6,
      assignments: (unit.topics || []).slice(0, 3).map((topic: string) => `${topic} analysis and implementation assignment`),
      practicalExercises: (unit.topics || []).slice(0, 4).map((topic: string) => `Hands-on laboratory exercise on ${topic}`),
      caseStudies: [
        `Industry case study related to ${unit.title}`,
        `Real-world application analysis in ${request.subject}`
      ]
    }));

    return {
      id: this.generateRandomId(),
      title: request.subject,
      code: this.generateCourseCode(request.department, request.semester),
      credits: request.credits || (request.courseType === 'practical' ? 2 : 4),
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: this.getPrerequisites(request.subject, request.semester),
      objectives: this.generateObjectives(request.subject),
      outcomes: this.generateCourseOutcomes(request.subject, cosCount),
      units: courseUnits,
      textBooks: syllabusData.recommendedTextbooks || this.generateTextBooks(request.subject),
      referenceBooks: syllabusData.referenceBooks || this.generateReferenceBooks(request.subject),
      onlineResources: this.getOnlineResources(request.subject),
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0
      },
      industryRelevance: {
        skills: this.getIndustrySkills(request.subject),
        tools: this.getIndustryTools(request.subject),
        certifications: this.getRelevantCertifications(request.subject),
        careerOpportunities: this.getCareerOpportunities(request.subject)
      },
      marketDemand: 'high' as const,
      difficultyLevel: request.semester <= 4 ? 3 : 4,
      createdAt: new Date(),
      generatedBy: `Enhanced Mock Service - ${syllabusData.confidence} confidence`
    };
  }

  async generateCourse(request: CourseGenerationRequest, userPreferences?: { cosCount: number; unitsCount: number }): Promise<Course> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const cosCount = userPreferences?.cosCount || 5;
    const unitsCount = userPreferences?.unitsCount || 5;

    const course: Course = {
      id: this.generateRandomId(),
      title: request.subject,
      code: this.generateCourseCode(request.department, request.semester),
      credits: request.courseType === 'practical' ? 2 : 4,
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: this.getPrerequisites(request.subject, request.semester),
      corequisites: [],
      industryRelevance: {
        skills: this.getIndustrySkills(request.subject),
        tools: this.getIndustryTools(request.subject),
        certifications: this.getRelevantCertifications(request.subject),
        careerOpportunities: this.getCareerOpportunities(request.subject)
      },
      marketDemand: 'high' as const,
      difficultyLevel: request.semester <= 4 ? 3 : 4,
      objectives: this.generateObjectives(request.subject),
      outcomes: this.generateCourseOutcomes(request.subject, cosCount),
      units: this.generateUnits(request.subject, unitsCount),
      textBooks: this.generateTextBooks(request.subject),
      referenceBooks: this.generateReferenceBooks(request.subject),
      onlineResources: this.getOnlineResources(request.subject),
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0
      },
      createdAt: new Date(),
      generatedBy: 'CourseCraft AI Designer (No API Key Required)'
    };

    return course;
  }

  private getPrerequisites(subject: string, semester: number): string[] {
    if (semester <= 2) return [];
    
    const prereqMap = {
      'Machine Learning': ['Data Structures', 'Statistics', 'Linear Algebra'],
      'Data Structures': ['Programming Fundamentals', 'Mathematics'],
      'Database Management': ['Data Structures', 'Software Engineering'],
      'Computer Networks': ['Operating Systems', 'Computer Organization']
    };

    return (prereqMap as any)[subject] || ['Mathematics', 'Programming Fundamentals'];
  }

  private generateObjectives(subject: string) {
    return [
      { id: 1, description: `Understand fundamental concepts and principles of ${subject}` },
      { id: 2, description: `Apply theoretical knowledge to solve practical problems in ${subject}` },
      { id: 3, description: `Analyze different approaches and methodologies in ${subject}` },
      { id: 4, description: `Evaluate solutions and make informed decisions in ${subject} applications` },
      { id: 5, description: `Design and implement systems using ${subject} concepts` }
    ];
  }

  private getIndustrySkills(subject: string): string[] {
    const skillMap = {
      'Machine Learning': ['Python', 'TensorFlow', 'PyTorch', 'Scikit-learn', 'Data Analysis'],
      'Data Structures': ['Algorithm Design', 'Problem Solving', 'Code Optimization', 'System Design'],
      'Database Management': ['SQL', 'Database Design', 'Query Optimization', 'Data Modeling'],
      'Computer Networks': ['Network Configuration', 'Security', 'Troubleshooting', 'Protocol Analysis']
    };

    return (skillMap as any)[subject] || ['Problem Solving', 'Analytical Thinking', 'Technical Communication'];
  }

  private getIndustryTools(subject: string): string[] {
    const toolMap = {
      'Machine Learning': ['Jupyter Notebook', 'Google Colab', 'Anaconda', 'MLflow'],
      'Data Structures': ['IDE', 'Debuggers', 'Profilers', 'Version Control'],
      'Database Management': ['MySQL', 'PostgreSQL', 'MongoDB', 'Oracle'],
      'Computer Networks': ['Wireshark', 'Cisco Packet Tracer', 'GNS3', 'Nmap']
    };

    return (toolMap as any)[subject] || ['Development Environment', 'Testing Tools', 'Documentation Tools'];
  }

  private getRelevantCertifications(subject: string): string[] {
    const certMap = {
      'Machine Learning': ['Google ML Engineer', 'AWS ML Specialty', 'Microsoft Azure AI'],
      'Data Structures': ['Coding Certifications', 'Algorithm Competitions', 'Technical Interviews'],
      'Database Management': ['Oracle DBA', 'Microsoft SQL Server', 'MongoDB Certified'],
      'Computer Networks': ['CCNA', 'CompTIA Network+', 'CISSP']
    };

    return (certMap as any)[subject] || ['Industry Certification', 'Professional Development'];
  }

  private getCareerOpportunities(subject: string): string[] {
    const careerMap = {
      'Machine Learning': ['ML Engineer', 'Data Scientist', 'AI Researcher', 'ML Consultant'],
      'Data Structures': ['Software Developer', 'Algorithm Engineer', 'System Architect', 'Technical Lead'],
      'Database Management': ['Database Administrator', 'Data Engineer', 'Backend Developer', 'Data Analyst'],
      'Computer Networks': ['Network Engineer', 'Security Analyst', 'System Administrator', 'Network Architect']
    };

    return (careerMap as any)[subject] || ['Technical Specialist', 'Consultant', 'Researcher', 'Industry Expert'];
  }

  private getOnlineResources(subject: string): string[] {
    const resourceMap = {
      'Machine Learning': [
        'https://www.coursera.org/specializations/machine-learning - Andrew Ng\'s Machine Learning Course',
        'https://www.edx.org/course/introduction-to-artificial-intelligence-ai - MIT AI Course',
        'https://www.kaggle.com/learn - Kaggle Learn ML Courses',
        'https://scikit-learn.org/stable/tutorial/index.html - Scikit-learn Official Tutorial',
        'https://www.tensorflow.org/tutorials - TensorFlow Official Tutorials',
        'https://pytorch.org/tutorials/ - PyTorch Official Tutorials'
      ],
      'Data Structures': [
        'https://www.geeksforgeeks.org/data-structures/ - GeeksforGeeks Data Structures',
        'https://www.coursera.org/specializations/data-structures-algorithms - UC San Diego Course',
        'https://visualgo.net/en - Data Structure Visualizations',
        'https://www.hackerrank.com/domains/data-structures - HackerRank Practice',
        'https://leetcode.com/explore/learn/ - LeetCode Learning Path',
        'https://www.youtube.com/playlist?list=PLBZBJbE_rGRV8D7XZ08LK6z-4zPoWzu5H - CS Dojo YouTube'
      ],
      'Database Management': [
        'https://www.w3schools.com/sql/ - W3Schools SQL Tutorial',
        'https://www.coursera.org/specializations/database-systems - Database Systems Specialization',
        'https://www.postgresql.org/docs/current/tutorial.html - PostgreSQL Tutorial',
        'https://dev.mysql.com/doc/mysql-tutorial-excerpt/8.0/en/ - MySQL Tutorial',
        'https://www.mongodb.com/university - MongoDB University',
        'https://sqlbolt.com/ - Interactive SQL Tutorial'
      ],
      'Computer Networks': [
        'https://www.coursera.org/learn/computer-networking - Computer Networking Course',
        'https://www.cisco.com/c/en/us/training-events/training-certifications/certifications/associate/ccna.html - CCNA Training',
        'https://www.wireshark.org/docs/ - Wireshark Documentation',
        'https://www.youtube.com/playlist?list=PLowKtXNTBypH19whXTVoG3oKSuOcw_XeW - Ben Eater Networking',
        'https://tools.ietf.org/rfc/ - Internet Engineering Task Force RFCs',
        'https://www.khanacademy.org/computing/computers-and-internet - Khan Academy Networking'
      ]
    };

    return (resourceMap as any)[subject] || [
      `https://www.coursera.org/search?query=${encodeURIComponent(subject)} - Coursera ${subject} Courses`,
      `https://www.edx.org/search?q=${encodeURIComponent(subject)} - edX ${subject} Courses`,
      `https://www.youtube.com/results?search_query=${encodeURIComponent(subject)}+tutorial - YouTube Tutorials`,
      `https://github.com/search?q=${encodeURIComponent(subject)} - GitHub Projects`,
      `https://stackoverflow.com/questions/tagged/${subject.toLowerCase().replace(/\s+/g, '-')} - Stack Overflow`,
      `https://medium.com/search?q=${encodeURIComponent(subject)} - Medium Articles`
    ];
  }

  private normalizeSubjectName(subject: string): string {
    // Create a mapping of common variations to standard names
    const subjectMappings: { [key: string]: string } = {
      'machine learning': 'Machine Learning',
      'ml': 'Machine Learning',
      'data structures': 'Data Structures',
      'ds': 'Data Structures',
      'operating systems': 'Operating Systems',
      'os': 'Operating Systems',
      'computer networks': 'Computer Networks',
      'networks': 'Computer Networks',
      'cn': 'Computer Networks',
      'database management systems': 'Database Management Systems',
      'dbms': 'Database Management Systems',
      'database': 'Database Management Systems',
      'software engineering': 'Software Engineering',
      'se': 'Software Engineering',
      'web development': 'Web Development',
      'web dev': 'Web Development',
      'artificial intelligence': 'Artificial Intelligence',
      'ai': 'Artificial Intelligence',
      'computer graphics': 'Computer Graphics',
      'graphics': 'Computer Graphics',
      'cg': 'Computer Graphics',
      'cybersecurity': 'Cybersecurity',
      'cyber security': 'Cybersecurity',
      'security': 'Cybersecurity',
      'mobile app development': 'Mobile App Development',
      'mobile development': 'Mobile App Development',
      'app development': 'Mobile App Development',
      'cloud computing': 'Cloud Computing',
      'cloud': 'Cloud Computing'
    };

    const normalized = subject.toLowerCase().trim();
    return subjectMappings[normalized] || subject;
  }

  private getDetailedCourseContent(subject: string) {
    // First try to get content from IIT/IISc syllabus research
    console.log('🔍 Researching syllabus content from top institutes for:', subject);
    const syllabusData = this.researchSyllabus(subject);

    if (syllabusData.confidence === 'high') {
      console.log('✅ Using detailed IIT/IISc syllabus content');

      // Create enhanced units from the syllabus data
      const enhancedUnits = syllabusData.units.map((unit: any) => ({
        title: unit.title,
        topics: unit.topics,
        topicDescriptions: unit.topics.reduce((acc: any, topic: string) => {
          acc[topic] = `Comprehensive study of ${topic} including theoretical foundations, practical applications, and current industry practices.`;
          return acc;
        }, {}),
        learningOutcomes: [
          `Understand the fundamental concepts of ${unit.title}`,
          `Apply the principles and techniques learned in practical scenarios`,
          `Analyze and evaluate different approaches within this unit's scope`
        ],
        contactHours: 15,
        practicalHours: 6,
        assignments: unit.topics.slice(0, 3).map((topic: string) => `${topic} analysis and implementation assignment`),
        practicalExercises: unit.topics.slice(0, 4).map((topic: string) => `Hands-on laboratory exercise on ${topic}`),
        caseStudies: [
          `Industry case study related to ${unit.title}`,
          `Real-world application analysis in ${subject}`
        ]
      }));

      return {
        units: enhancedUnits,
        // Store the original units count for intelligent extension
        originalUnitsCount: syllabusData.units.length,
        hasDetailedContent: true
      };
    }

    console.log('⚠️ Using fallback detailed content');
    // Normalize subject name for better matching
    const normalizedSubject = this.normalizeSubjectName(subject);

    const contentMap: { [key: string]: any } = {
      'Machine Learning': {
        units: [
          {
            title: 'Introduction to Machine Learning and Mathematical Foundations',
            topics: [
              'Supervised vs Unsupervised vs Reinforcement Learning',
              'Vector Operations and Matrix Multiplication',
              'Probability Distributions and Bayes Theorem',
              'Feature Scaling and Normalization Techniques',
              'Principal Component Analysis (PCA)'
            ],
            topicDescriptions: {
              'Supervised vs Unsupervised vs Reinforcement Learning': 'Classification of ML paradigms with examples: regression, clustering, Q-learning.',
              'Vector Operations and Matrix Multiplication': 'Dot products, matrix operations, eigenvalues, eigenvectors for ML computations.',
              'Probability Distributions and Bayes Theorem': 'Gaussian, Bernoulli, Multinomial distributions, conditional probability, Bayes rule.',
              'Feature Scaling and Normalization Techniques': 'Min-max scaling, Z-score normalization, robust scaling for data preprocessing.',
              'Principal Component Analysis (PCA)': 'Dimensionality reduction, variance maximization, eigenvalue decomposition.'
            },
            learningOutcomes: [
              'Understand the fundamental concepts and applications of machine learning',
              'Apply mathematical foundations including linear algebra and statistics to ML problems',
              'Implement data preprocessing techniques for real-world datasets'
            ],
            contactHours: 15,
            practicalHours: 6,
            assignments: [
              'Mathematical foundations quiz and problem solving',
              'Data preprocessing project using Python/R',
              'Exploratory data analysis on a real dataset'
            ],
            practicalExercises: [
              'Implementing basic statistical functions in Python',
              'Data visualization using matplotlib and seaborn',
              'Feature engineering on Titanic dataset'
            ],
            caseStudies: [
              'Netflix recommendation system overview',
              'Google search algorithm basics'
            ]
          },
          {
            title: 'Supervised Learning Algorithms',
            topics: [
              'Linear Regression with Gradient Descent',
              'Logistic Regression and Sigmoid Function',
              'Decision Tree Construction and Pruning',
              'Support Vector Machine with Kernel Trick',
              'K-Nearest Neighbors Algorithm'
            ],
            topicDescriptions: {
              'Linear Regression with Gradient Descent': 'Cost function minimization, batch and stochastic gradient descent, learning rate optimization.',
              'Logistic Regression and Sigmoid Function': 'Binary classification, sigmoid activation, maximum likelihood estimation, regularization.',
              'Decision Tree Construction and Pruning': 'Information gain, Gini impurity, tree splitting criteria, post-pruning techniques.',
              'Support Vector Machine with Kernel Trick': 'Hyperplane separation, soft margin, RBF kernel, polynomial kernel, parameter tuning.',
              'K-Nearest Neighbors Algorithm': 'Distance metrics (Euclidean, Manhattan), k-value selection, weighted voting, curse of dimensionality.'
            },
            learningOutcomes: [
              'Implement and compare various supervised learning algorithms',
              'Evaluate model performance using appropriate metrics',
              'Select optimal algorithms for specific problem domains'
            ],
            contactHours: 18,
            practicalHours: 8,
            assignments: [
              'Comparative analysis of classification algorithms',
              'Regression analysis on housing price prediction',
              'Model selection and hyperparameter tuning project'
            ],
            practicalExercises: [
              'Implementing linear regression from scratch',
              'Building decision trees using scikit-learn',
              'SVM implementation for text classification'
            ],
            caseStudies: [
              'Credit card fraud detection system',
              'Medical diagnosis using ML algorithms'
            ]
          },
          {
            title: 'Unsupervised Learning and Clustering',
            topics: [
              'K-Means Clustering Algorithm',
              'Hierarchical Clustering with Dendrograms',
              'DBSCAN Density-Based Clustering',
              'Apriori Algorithm for Association Rules',
              'Gaussian Mixture Models (GMM)'
            ],
            topicDescriptions: {
              'K-Means Clustering Algorithm': 'Centroid initialization, Lloyd\'s algorithm, K-means++, elbow method, silhouette coefficient.',
              'Hierarchical Clustering with Dendrograms': 'Agglomerative clustering, single/complete/average linkage, dendrogram interpretation.',
              'DBSCAN Density-Based Clustering': 'Core points, border points, noise detection, epsilon and min-points parameters.',
              'Apriori Algorithm for Association Rules': 'Frequent itemset mining, support and confidence thresholds, lift and conviction measures.',
              'Gaussian Mixture Models (GMM)': 'Expectation-Maximization algorithm, probabilistic clustering, model selection with BIC/AIC.'
            },
            learningOutcomes: [
              'Apply clustering algorithms to discover patterns in unlabeled data',
              'Implement dimensionality reduction techniques for data visualization',
              'Extract association rules from transactional data'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Customer segmentation using clustering algorithms',
              'Dimensionality reduction on high-dimensional datasets',
              'Market basket analysis project'
            ],
            practicalExercises: [
              'K-means clustering on customer data',
              'PCA implementation for image compression',
              'Association rule mining on retail data'
            ],
            caseStudies: [
              'Customer segmentation for e-commerce',
              'Gene expression analysis using clustering'
            ]
          },
          {
            title: 'Neural Networks and Deep Learning',
            topics: [
              'Artificial Neural Networks',
              'Backpropagation Algorithm',
              'Convolutional Neural Networks (CNN)',
              'Recurrent Neural Networks (RNN)',
              'Deep Learning Frameworks'
            ],
            topicDescriptions: {
              'Artificial Neural Networks': 'Perceptron, multi-layer perceptrons, activation functions, and network architectures.',
              'Backpropagation Algorithm': 'Gradient computation, chain rule, weight updates, and optimization techniques.',
              'Convolutional Neural Networks (CNN)': 'Convolution layers, pooling, feature maps, and applications in computer vision.',
              'Recurrent Neural Networks (RNN)': 'LSTM, GRU, sequence modeling, and applications in NLP.',
              'Deep Learning Frameworks': 'TensorFlow, PyTorch, Keras, and model deployment strategies.'
            },
            learningOutcomes: [
              'Design and implement neural network architectures',
              'Apply deep learning techniques to computer vision and NLP problems',
              'Use modern deep learning frameworks for model development'
            ],
            contactHours: 20,
            practicalHours: 10,
            assignments: [
              'Neural network implementation from scratch',
              'Image classification using CNN',
              'Text sentiment analysis using RNN'
            ],
            practicalExercises: [
              'Building neural networks with TensorFlow',
              'CNN for handwritten digit recognition',
              'LSTM for stock price prediction'
            ],
            caseStudies: [
              'AlexNet and ImageNet competition',
              'Google Translate neural machine translation'
            ]
          },
          {
            title: 'Advanced Topics and Applications',
            topics: [
              'Ensemble Methods',
              'Natural Language Processing',
              'Computer Vision Applications',
              'Reinforcement Learning Basics',
              'ML Model Deployment and MLOps'
            ],
            topicDescriptions: {
              'Ensemble Methods': 'Bagging, boosting, AdaBoost, Gradient Boosting, and XGBoost algorithms.',
              'Natural Language Processing': 'Text preprocessing, TF-IDF, word embeddings, and transformer models.',
              'Computer Vision Applications': 'Object detection, image segmentation, face recognition, and OpenCV.',
              'Reinforcement Learning Basics': 'Q-learning, policy gradients, and applications in game playing.',
              'ML Model Deployment and MLOps': 'Model serving, monitoring, versioning, and production pipelines.'
            },
            learningOutcomes: [
              'Implement ensemble methods for improved model performance',
              'Apply ML techniques to NLP and computer vision problems',
              'Deploy ML models in production environments'
            ],
            contactHours: 18,
            practicalHours: 8,
            assignments: [
              'Ensemble methods comparison project',
              'NLP application development',
              'End-to-end ML project with deployment'
            ],
            practicalExercises: [
              'XGBoost for structured data prediction',
              'Text classification with transformers',
              'Model deployment using Flask/FastAPI'
            ],
            caseStudies: [
              'Kaggle competition winning solutions',
              'Industry ML deployment case studies'
            ]
          }
        ]
      },
      'Data Structures': {
        units: [
          {
            title: 'Introduction to Data Structures and Algorithm Analysis',
            topics: [
              'Abstract Data Types (ADT)',
              'Big O Notation and Complexity Analysis',
              'Array Operations and Memory Layout',
              'String Matching Algorithms',
              'Pointer Arithmetic and Memory Management'
            ],
            topicDescriptions: {
              'Abstract Data Types (ADT)': 'Interface vs implementation, encapsulation, data abstraction, ADT specifications.',
              'Big O Notation and Complexity Analysis': 'Time complexity, space complexity, best/average/worst case, asymptotic analysis.',
              'Array Operations and Memory Layout': 'Array indexing, insertion, deletion, searching, memory allocation, cache locality.',
              'String Matching Algorithms': 'Naive string matching, KMP algorithm, Boyer-Moore algorithm, Rabin-Karp algorithm.',
              'Pointer Arithmetic and Memory Management': 'Memory allocation, deallocation, pointer operations, memory leaks, garbage collection.'
            },
            learningOutcomes: [
              'Analyze time and space complexity of algorithms using Big O notation',
              'Implement efficient array-based data structures',
              'Apply string processing algorithms for text manipulation'
            ],
            contactHours: 12,
            practicalHours: 6,
            assignments: [
              'Algorithm complexity analysis problems',
              'Dynamic array implementation in C++/Java',
              'String processing algorithms implementation'
            ],
            practicalExercises: [
              'Memory allocation and deallocation exercises',
              'Array sorting and searching implementations',
              'Pattern matching algorithm coding'
            ],
            caseStudies: [
              'Google search indexing using arrays',
              'Database indexing strategies'
            ]
          },
          {
            title: 'Linear Data Structures',
            topics: [
              'Stack Implementation and Applications',
              'Queue Implementation with Circular Arrays',
              'Singly and Doubly Linked Lists',
              'Priority Queue with Binary Heap',
              'Deque (Double-Ended Queue)'
            ],
            topicDescriptions: {
              'Stack Implementation and Applications': 'LIFO operations, array-based and linked implementation, expression evaluation, function call stack.',
              'Queue Implementation with Circular Arrays': 'FIFO operations, circular array implementation, front and rear pointers, queue overflow handling.',
              'Singly and Doubly Linked Lists': 'Node structure, insertion/deletion operations, traversal, memory allocation, pointer manipulation.',
              'Priority Queue with Binary Heap': 'Heap property, heapify operations, insertion and deletion in O(log n), heap sort algorithm.',
              'Deque (Double-Ended Queue)': 'Double-ended operations, circular buffer implementation, applications in sliding window problems.'
            },
            learningOutcomes: [
              'Implement and apply stack data structure for problem solving',
              'Design efficient queue-based solutions for real-world problems',
              'Compare different linked list implementations and their use cases'
            ],
            contactHours: 14,
            practicalHours: 8,
            assignments: [
              'Stack-based expression evaluator',
              'Queue simulation for operating systems',
              'Linked list implementation with memory management'
            ],
            practicalExercises: [
              'Balanced parentheses checker using stacks',
              'BFS implementation using queues',
              'Music playlist using doubly linked lists'
            ],
            caseStudies: [
              'Browser history implementation',
              'Print queue management system'
            ]
          },
          {
            title: 'Trees and Tree Algorithms',
            topics: [
              'Binary Tree Traversal Algorithms',
              'Binary Search Tree Operations',
              'AVL Tree Rotations and Balancing',
              'Heap Sort Algorithm',
              'Huffman Coding Algorithm'
            ],
            topicDescriptions: {
              'Binary Tree Traversal Algorithms': 'Inorder, preorder, postorder recursive and iterative implementations, level-order traversal using queue.',
              'Binary Search Tree Operations': 'BST insertion, deletion, search operations, finding min/max, inorder successor/predecessor.',
              'AVL Tree Rotations and Balancing': 'Left rotation, right rotation, left-right and right-left rotations, balance factor calculation.',
              'Heap Sort Algorithm': 'Build max-heap, extract maximum, heapify operation, in-place sorting with O(n log n) complexity.',
              'Huffman Coding Algorithm': 'Frequency-based encoding, building Huffman tree, optimal prefix codes, compression applications.'
            },
            learningOutcomes: [
              'Implement various tree data structures and traversal algorithms',
              'Design self-balancing tree structures for optimal performance',
              'Apply heap data structure for priority-based operations'
            ],
            contactHours: 16,
            practicalHours: 10,
            assignments: [
              'BST implementation with all operations',
              'AVL tree with automatic balancing',
              'Heap-based priority queue system'
            ],
            practicalExercises: [
              'File system directory structure using trees',
              'Expression evaluation using expression trees',
              'Huffman coding using binary trees'
            ],
            caseStudies: [
              'Database indexing using B-trees',
              'Compiler syntax tree construction'
            ]
          },
          {
            title: 'Graphs and Graph Algorithms',
            topics: [
              'Depth-First Search (DFS) Algorithm',
              'Breadth-First Search (BFS) Algorithm',
              'Dijkstra\'s Shortest Path Algorithm',
              'Kruskal\'s Minimum Spanning Tree',
              'Floyd-Warshall All-Pairs Shortest Path'
            ],
            topicDescriptions: {
              'Depth-First Search (DFS) Algorithm': 'Recursive and iterative DFS implementation, DFS tree, cycle detection, topological sorting.',
              'Breadth-First Search (BFS) Algorithm': 'Queue-based BFS implementation, shortest path in unweighted graphs, level-order traversal.',
              'Dijkstra\'s Shortest Path Algorithm': 'Single-source shortest path, priority queue implementation, relaxation technique, path reconstruction.',
              'Kruskal\'s Minimum Spanning Tree': 'Edge-based MST algorithm, union-find data structure, cycle detection, greedy approach.',
              'Floyd-Warshall All-Pairs Shortest Path': 'Dynamic programming approach, all-pairs shortest distances, transitive closure, negative cycle detection.'
            },
            learningOutcomes: [
              'Implement graph data structures and basic operations',
              'Apply graph traversal algorithms for connectivity problems',
              'Solve shortest path and minimum spanning tree problems'
            ],
            contactHours: 18,
            practicalHours: 10,
            assignments: [
              'Social network analysis using graphs',
              'Route planning system using shortest path algorithms',
              'Network design using MST algorithms'
            ],
            practicalExercises: [
              'DFS and BFS implementation on various graph types',
              'Dijkstra\'s algorithm for GPS navigation',
              'Kruskal\'s algorithm for network optimization'
            ],
            caseStudies: [
              'Google Maps routing algorithms',
              'Social media friend recommendation systems'
            ]
          },
          {
            title: 'Advanced Data Structures and Hashing',
            topics: [
              'Hash Tables and Hash Functions',
              'Collision Resolution Techniques',
              'Advanced Tree Structures',
              'Trie Data Structure',
              'Disjoint Set Union (DSU)'
            ],
            topicDescriptions: {
              'Hash Tables and Hash Functions': 'Hash table design, hash function properties, load factor, and performance analysis.',
              'Collision Resolution Techniques': 'Chaining, open addressing, linear probing, quadratic probing, and double hashing.',
              'Advanced Tree Structures': 'B-trees, B+ trees, Red-Black trees, and their applications in databases and file systems.',
              'Trie Data Structure': 'Prefix trees, string storage and retrieval, autocomplete systems, and compressed tries.',
              'Disjoint Set Union (DSU)': 'Union-Find data structure, path compression, union by rank, and applications in connectivity problems.'
            },
            learningOutcomes: [
              'Design efficient hash-based data structures',
              'Implement advanced tree structures for specialized applications',
              'Apply trie data structure for string processing problems'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Hash table implementation with collision handling',
              'Autocomplete system using trie data structure',
              'Union-Find for dynamic connectivity problems'
            ],
            practicalExercises: [
              'Dictionary implementation using hash tables',
              'Search engine indexing using tries',
              'Network connectivity using DSU'
            ],
            caseStudies: [
              'Database indexing using B+ trees',
              'Spell checker implementation using tries'
            ]
          }
        ]
      },
      'Operating Systems': {
        units: [
          {
            title: 'Process Management and CPU Scheduling',
            topics: [
              'Process Control Block (PCB)',
              'First Come First Serve (FCFS) Scheduling',
              'Shortest Job First (SJF) Scheduling',
              'Round Robin Scheduling Algorithm',
              'Priority Scheduling with Aging'
            ],
            topicDescriptions: {
              'Process Control Block (PCB)': 'Process state, program counter, CPU registers, memory management information, I/O status.',
              'First Come First Serve (FCFS) Scheduling': 'Non-preemptive scheduling, convoy effect, average waiting time calculation.',
              'Shortest Job First (SJF) Scheduling': 'Preemptive and non-preemptive SJF, optimal average waiting time, starvation problem.',
              'Round Robin Scheduling Algorithm': 'Time quantum selection, context switching overhead, response time optimization.',
              'Priority Scheduling with Aging': 'Static and dynamic priorities, priority inversion, aging technique to prevent starvation.'
            },
            learningOutcomes: [
              'Implement various CPU scheduling algorithms',
              'Calculate performance metrics for scheduling algorithms',
              'Analyze trade-offs between different scheduling policies'
            ],
            contactHours: 14,
            practicalHours: 8,
            assignments: [
              'CPU scheduling simulator implementation',
              'Performance comparison of scheduling algorithms',
              'Process synchronization project'
            ],
            practicalExercises: [
              'FCFS and SJF algorithm implementation',
              'Round Robin scheduler with time quantum',
              'Priority queue implementation for scheduling'
            ],
            caseStudies: [
              'Linux CFS (Completely Fair Scheduler)',
              'Windows thread scheduling'
            ]
          },
          {
            title: 'Memory Management Techniques',
            topics: [
              'Paging and Page Replacement Algorithms',
              'FIFO Page Replacement Algorithm',
              'LRU (Least Recently Used) Algorithm',
              'Optimal Page Replacement Algorithm',
              'Segmentation and Virtual Memory'
            ],
            topicDescriptions: {
              'Paging and Page Replacement Algorithms': 'Page tables, page faults, demand paging, page replacement strategies.',
              'FIFO Page Replacement Algorithm': 'First-in-first-out replacement, Belady\'s anomaly, implementation using queue.',
              'LRU (Least Recently Used) Algorithm': 'Stack-based implementation, counter-based implementation, approximation algorithms.',
              'Optimal Page Replacement Algorithm': 'Belady\'s optimal algorithm, theoretical minimum page faults, comparison benchmark.',
              'Segmentation and Virtual Memory': 'Logical vs physical addresses, segment tables, memory protection, virtual address translation.'
            },
            learningOutcomes: [
              'Implement page replacement algorithms',
              'Analyze memory management performance',
              'Design virtual memory systems'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Page replacement algorithm simulator',
              'Virtual memory management system',
              'Memory allocation strategies comparison'
            ],
            practicalExercises: [
              'LRU cache implementation',
              'Page table management',
              'Memory fragmentation analysis'
            ],
            caseStudies: [
              'Linux memory management',
              'Android memory optimization'
            ]
          }
        ]
      },
      'Computer Networks': {
        units: [
          {
            title: 'Network Protocols and OSI Model',
            topics: [
              'OSI Seven Layer Model',
              'TCP Three-Way Handshake Protocol',
              'UDP Datagram Protocol',
              'HTTP/HTTPS Request-Response Cycle',
              'DNS Resolution and BIND'
            ],
            topicDescriptions: {
              'OSI Seven Layer Model': 'Physical, Data Link, Network, Transport, Session, Presentation, Application layers with Ethernet, IP, TCP protocols.',
              'TCP Three-Way Handshake Protocol': 'SYN, SYN-ACK, ACK sequence, sequence numbers, window scaling, congestion control.',
              'UDP Datagram Protocol': 'Connectionless communication, UDP header structure, checksum calculation, real-time applications.',
              'HTTP/HTTPS Request-Response Cycle': 'HTTP methods (GET, POST, PUT, DELETE), status codes (200, 404, 500), SSL/TLS encryption.',
              'DNS Resolution and BIND': 'Domain name hierarchy, A/AAAA/CNAME records, recursive/iterative queries, DNS caching.'
            },
            learningOutcomes: [
              'Implement TCP/UDP socket programming',
              'Analyze network packets using Wireshark',
              'Configure DNS and web servers'
            ],
            contactHours: 15,
            practicalHours: 10,
            assignments: [
              'TCP chat application implementation',
              'HTTP web server from scratch',
              'Network packet analyzer tool'
            ],
            practicalExercises: [
              'Wireshark packet capture and analysis',
              'Socket programming in C/Python',
              'DNS server configuration with BIND'
            ],
            caseStudies: [
              'Cloudflare CDN architecture',
              'Netflix content delivery optimization'
            ]
          },
          {
            title: 'Routing Algorithms and Network Security',
            topics: [
              'Dijkstra\'s Shortest Path Routing',
              'Bellman-Ford Distance Vector Algorithm',
              'OSPF Link State Routing Protocol',
              'BGP Border Gateway Protocol',
              'IPSec and VPN Tunneling'
            ],
            topicDescriptions: {
              'Dijkstra\'s Shortest Path Routing': 'Link-state routing, shortest path tree construction, OSPF implementation, network topology.',
              'Bellman-Ford Distance Vector Algorithm': 'Distance vector routing, count-to-infinity problem, split horizon, RIP protocol.',
              'OSPF Link State Routing Protocol': 'Link State Advertisement (LSA), area-based routing, designated router election.',
              'BGP Border Gateway Protocol': 'Autonomous systems, path vector routing, BGP attributes, internet routing policies.',
              'IPSec and VPN Tunneling': 'Authentication Header (AH), Encapsulating Security Payload (ESP), IKE protocol, tunnel mode.'
            },
            learningOutcomes: [
              'Implement routing algorithms for network optimization',
              'Configure OSPF and BGP routing protocols',
              'Design secure VPN connections using IPSec'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Routing algorithm simulation',
              'OSPF network configuration',
              'VPN setup and security analysis'
            ],
            practicalExercises: [
              'Cisco router configuration with OSPF',
              'BGP routing policy implementation',
              'IPSec VPN tunnel establishment'
            ],
            caseStudies: [
              'Internet backbone routing architecture',
              'Corporate VPN security implementations'
            ]
          }
        ]
      },
      'Database Management Systems': {
        units: [
          {
            title: 'Relational Database Design and Normalization',
            topics: [
              'Entity-Relationship (ER) Modeling',
              'First Normal Form (1NF)',
              'Second Normal Form (2NF)',
              'Third Normal Form (3NF)',
              'Boyce-Codd Normal Form (BCNF)'
            ],
            topicDescriptions: {
              'Entity-Relationship (ER) Modeling': 'Entity sets, relationship sets, attributes, cardinality constraints, weak entities, ER diagrams.',
              'First Normal Form (1NF)': 'Atomic values, elimination of repeating groups, primary key identification, table structure.',
              'Second Normal Form (2NF)': 'Partial dependency elimination, composite primary keys, functional dependencies.',
              'Third Normal Form (3NF)': 'Transitive dependency removal, non-key attribute dependencies, decomposition.',
              'Boyce-Codd Normal Form (BCNF)': 'Stronger 3NF condition, determinant analysis, lossless decomposition.'
            },
            learningOutcomes: [
              'Design normalized relational database schemas',
              'Apply normalization techniques to eliminate redundancy',
              'Create ER diagrams for complex business requirements'
            ],
            contactHours: 14,
            practicalHours: 8,
            assignments: [
              'University database ER design',
              'Normalization of denormalized tables',
              'Database schema optimization project'
            ],
            practicalExercises: [
              'ER diagram creation using MySQL Workbench',
              'Normalization exercises with sample data',
              'Database design for e-commerce system'
            ],
            caseStudies: [
              'Amazon product catalog database design',
              'Banking system database normalization'
            ]
          },
          {
            title: 'SQL Query Processing and Optimization',
            topics: [
              'SQL SELECT with JOIN Operations',
              'Aggregate Functions and GROUP BY',
              'Subqueries and Correlated Queries',
              'Query Execution Plans',
              'Index Optimization Strategies'
            ],
            topicDescriptions: {
              'SQL SELECT with JOIN Operations': 'INNER JOIN, LEFT/RIGHT OUTER JOIN, FULL OUTER JOIN, CROSS JOIN, self-joins.',
              'Aggregate Functions and GROUP BY': 'COUNT, SUM, AVG, MIN, MAX functions, HAVING clause, window functions.',
              'Subqueries and Correlated Queries': 'Nested SELECT statements, EXISTS operator, IN/NOT IN, correlated subqueries.',
              'Query Execution Plans': 'Query optimizer, execution plan analysis, cost-based optimization, statistics.',
              'Index Optimization Strategies': 'B-tree indexes, hash indexes, composite indexes, query performance tuning.'
            },
            learningOutcomes: [
              'Write complex SQL queries with multiple joins',
              'Optimize query performance using indexes',
              'Analyze and interpret query execution plans'
            ],
            contactHours: 16,
            practicalHours: 10,
            assignments: [
              'Complex SQL query development',
              'Database performance optimization',
              'Query execution plan analysis'
            ],
            practicalExercises: [
              'Advanced SQL queries on sample databases',
              'Index creation and performance testing',
              'Query optimization using EXPLAIN plans'
            ],
            caseStudies: [
              'Netflix recommendation query optimization',
              'Financial trading system query performance'
            ]
          }
        ]
      },
      'Software Engineering': {
        units: [
          {
            title: 'Software Development Life Cycle Models',
            topics: [
              'Waterfall Model',
              'Agile Methodology and Scrum Framework',
              'Spiral Model with Risk Analysis',
              'DevOps and Continuous Integration',
              'Test-Driven Development (TDD)'
            ],
            topicDescriptions: {
              'Waterfall Model': 'Sequential phases, requirements analysis, design, implementation, testing, maintenance.',
              'Agile Methodology and Scrum Framework': 'Iterative development, sprints, user stories, daily standups, retrospectives.',
              'Spiral Model with Risk Analysis': 'Risk-driven approach, prototyping, iterative refinement, risk mitigation strategies.',
              'DevOps and Continuous Integration': 'CI/CD pipelines, automated testing, deployment automation, infrastructure as code.',
              'Test-Driven Development (TDD)': 'Red-Green-Refactor cycle, unit testing, test automation, code coverage.'
            },
            learningOutcomes: [
              'Select appropriate SDLC models for different projects',
              'Implement Agile practices in software development',
              'Design CI/CD pipelines for automated deployment'
            ],
            contactHours: 15,
            practicalHours: 8,
            assignments: [
              'SDLC model comparison analysis',
              'Agile project planning and execution',
              'CI/CD pipeline implementation'
            ],
            practicalExercises: [
              'Scrum simulation with real project',
              'Jenkins CI/CD pipeline setup',
              'TDD implementation with unit tests'
            ],
            caseStudies: [
              'Spotify Agile scaling model',
              'Netflix DevOps practices'
            ]
          },
          {
            title: 'Design Patterns and Software Architecture',
            topics: [
              'Singleton Design Pattern',
              'Factory Method Pattern',
              'Observer Pattern',
              'Model-View-Controller (MVC) Architecture',
              'Microservices Architecture Pattern'
            ],
            topicDescriptions: {
              'Singleton Design Pattern': 'Single instance creation, thread safety, lazy initialization, enum singleton.',
              'Factory Method Pattern': 'Object creation abstraction, concrete factories, product hierarchies.',
              'Observer Pattern': 'Subject-observer relationship, event notification, loose coupling, publish-subscribe.',
              'Model-View-Controller (MVC) Architecture': 'Separation of concerns, model data management, view presentation, controller logic.',
              'Microservices Architecture Pattern': 'Service decomposition, API gateways, service discovery, distributed systems.'
            },
            learningOutcomes: [
              'Implement common design patterns in software projects',
              'Design scalable software architectures',
              'Apply architectural patterns for maintainable code'
            ],
            contactHours: 16,
            practicalHours: 10,
            assignments: [
              'Design pattern implementation project',
              'MVC web application development',
              'Microservices architecture design'
            ],
            practicalExercises: [
              'Gang of Four patterns implementation',
              'Spring MVC application development',
              'Docker containerized microservices'
            ],
            caseStudies: [
              'Netflix microservices architecture',
              'Uber\'s service-oriented architecture'
            ]
          }
        ]
      },
      'Web Development': {
        units: [
          {
            title: 'Frontend Technologies and Frameworks',
            topics: [
              'HTML5 Semantic Elements',
              'CSS3 Flexbox and Grid Layout',
              'JavaScript ES6+ Features',
              'React.js Component Architecture',
              'Vue.js Reactive Data Binding'
            ],
            topicDescriptions: {
              'HTML5 Semantic Elements': 'Article, section, nav, header, footer elements, accessibility, SEO optimization.',
              'CSS3 Flexbox and Grid Layout': 'Flexible box layout, CSS Grid, responsive design, media queries.',
              'JavaScript ES6+ Features': 'Arrow functions, destructuring, promises, async/await, modules, classes.',
              'React.js Component Architecture': 'JSX syntax, component lifecycle, hooks (useState, useEffect), virtual DOM.',
              'Vue.js Reactive Data Binding': 'Template syntax, directives, computed properties, Vuex state management.'
            },
            learningOutcomes: [
              'Build responsive web interfaces using modern CSS',
              'Develop interactive web applications with JavaScript',
              'Create component-based applications using React/Vue'
            ],
            contactHours: 16,
            practicalHours: 12,
            assignments: [
              'Responsive portfolio website',
              'React todo application',
              'Vue.js e-commerce frontend'
            ],
            practicalExercises: [
              'CSS Grid layout implementation',
              'React hooks and state management',
              'Vue.js component development'
            ],
            caseStudies: [
              'Airbnb React component library',
              'GitLab Vue.js migration'
            ]
          },
          {
            title: 'Backend Development and APIs',
            topics: [
              'Node.js Express Framework',
              'RESTful API Design Principles',
              'MongoDB Database Operations',
              'JWT Authentication Implementation',
              'GraphQL Query Language'
            ],
            topicDescriptions: {
              'Node.js Express Framework': 'Middleware functions, routing, error handling, static file serving.',
              'RESTful API Design Principles': 'HTTP methods, status codes, resource naming, HATEOAS, API versioning.',
              'MongoDB Database Operations': 'CRUD operations, aggregation pipeline, indexing, schema design.',
              'JWT Authentication Implementation': 'JSON Web Tokens, token-based authentication, refresh tokens, security.',
              'GraphQL Query Language': 'Schema definition, resolvers, queries, mutations, subscriptions.'
            },
            learningOutcomes: [
              'Build RESTful APIs using Node.js and Express',
              'Implement secure authentication systems',
              'Design and query GraphQL APIs'
            ],
            contactHours: 18,
            practicalHours: 10,
            assignments: [
              'RESTful API development',
              'Authentication system implementation',
              'GraphQL API design'
            ],
            practicalExercises: [
              'Express.js middleware development',
              'MongoDB aggregation queries',
              'JWT token management'
            ],
            caseStudies: [
              'GitHub GraphQL API design',
              'Stripe payment API architecture'
            ]
          }
        ]
      },
      'Artificial Intelligence': {
        units: [
          {
            title: 'Search Algorithms and Problem Solving',
            topics: [
              'Breadth-First Search (BFS)',
              'Depth-First Search (DFS)',
              'A* Search Algorithm',
              'Minimax Algorithm with Alpha-Beta Pruning',
              'Genetic Algorithm Optimization'
            ],
            topicDescriptions: {
              'Breadth-First Search (BFS)': 'Queue-based search, optimal path finding, time complexity O(b^d), space complexity.',
              'Depth-First Search (DFS)': 'Stack-based search, depth-limited search, iterative deepening, memory efficiency.',
              'A* Search Algorithm': 'Heuristic search, f(n) = g(n) + h(n), admissible heuristics, optimal pathfinding.',
              'Minimax Algorithm with Alpha-Beta Pruning': 'Game tree search, adversarial search, pruning optimization, game AI.',
              'Genetic Algorithm Optimization': 'Population-based search, selection, crossover, mutation, fitness functions.'
            },
            learningOutcomes: [
              'Implement search algorithms for problem solving',
              'Apply heuristic search techniques',
              'Design AI agents for game playing'
            ],
            contactHours: 15,
            practicalHours: 10,
            assignments: [
              '8-puzzle solver using A*',
              'Chess AI with minimax',
              'Genetic algorithm for TSP'
            ],
            practicalExercises: [
              'Pathfinding in grid worlds',
              'Game tree implementation',
              'Optimization problem solving'
            ],
            caseStudies: [
              'Google Maps route optimization',
              'DeepBlue chess algorithm'
            ]
          },
          {
            title: 'Knowledge Representation and Expert Systems',
            topics: [
              'Propositional Logic and Inference',
              'First-Order Logic (FOL)',
              'Semantic Networks',
              'Production Rule Systems',
              'Fuzzy Logic and Fuzzy Sets'
            ],
            topicDescriptions: {
              'Propositional Logic and Inference': 'Boolean logic, truth tables, resolution theorem proving, modus ponens.',
              'First-Order Logic (FOL)': 'Predicates, quantifiers, unification, resolution in FOL, knowledge bases.',
              'Semantic Networks': 'Node-link representations, inheritance, IS-A relationships, frame-based systems.',
              'Production Rule Systems': 'IF-THEN rules, forward chaining, backward chaining, conflict resolution.',
              'Fuzzy Logic and Fuzzy Sets': 'Membership functions, fuzzy operations, defuzzification, fuzzy control.'
            },
            learningOutcomes: [
              'Represent knowledge using logical formalism',
              'Build expert systems with rule-based reasoning',
              'Apply fuzzy logic for uncertain reasoning'
            ],
            contactHours: 16,
            practicalHours: 8,
            assignments: [
              'Logic-based knowledge base',
              'Expert system development',
              'Fuzzy control system'
            ],
            practicalExercises: [
              'Prolog programming for logic',
              'Rule-based system implementation',
              'Fuzzy logic controller design'
            ],
            caseStudies: [
              'MYCIN medical expert system',
              'Fuzzy logic in washing machines'
            ]
          }
        ]
      },
      'Computer Graphics': {
        units: [
          {
            title: '2D Graphics and Transformations',
            topics: [
              'Bresenham\'s Line Drawing Algorithm',
              'DDA (Digital Differential Analyzer)',
              'Midpoint Circle Algorithm',
              '2D Transformation Matrices',
              'Cohen-Sutherland Line Clipping'
            ],
            topicDescriptions: {
              'Bresenham\'s Line Drawing Algorithm': 'Integer-only line drawing, decision parameter, pixel-level accuracy, efficiency.',
              'DDA (Digital Differential Analyzer)': 'Incremental line drawing, floating-point calculations, step-by-step approach.',
              'Midpoint Circle Algorithm': 'Circle drawing using decision parameter, 8-way symmetry, integer arithmetic.',
              '2D Transformation Matrices': 'Translation, rotation, scaling matrices, homogeneous coordinates, composite transformations.',
              'Cohen-Sutherland Line Clipping': 'Outcodes, trivial acceptance/rejection, line clipping against rectangular windows.'
            },
            learningOutcomes: [
              'Implement fundamental graphics algorithms',
              'Apply 2D transformations to geometric objects',
              'Develop line and circle drawing routines'
            ],
            contactHours: 14,
            practicalHours: 10,
            assignments: [
              'Graphics library implementation',
              '2D transformation system',
              'Line clipping algorithm'
            ],
            practicalExercises: [
              'OpenGL 2D graphics programming',
              'Transformation matrix calculations',
              'Interactive drawing application'
            ],
            caseStudies: [
              'AutoCAD 2D drawing algorithms',
              'Adobe Illustrator vector graphics'
            ]
          },
          {
            title: '3D Graphics and Rendering',
            topics: [
              'Z-Buffer Depth Testing',
              'Phong Illumination Model',
              'Gouraud Shading Technique',
              'Ray Tracing Algorithm',
              'Texture Mapping and UV Coordinates'
            ],
            topicDescriptions: {
              'Z-Buffer Depth Testing': 'Hidden surface removal, depth buffer algorithm, z-fighting, depth precision.',
              'Phong Illumination Model': 'Ambient, diffuse, specular lighting, surface normals, light attenuation.',
              'Gouraud Shading Technique': 'Vertex-based shading, color interpolation, smooth shading across polygons.',
              'Ray Tracing Algorithm': 'Ray-object intersection, reflection, refraction, global illumination.',
              'Texture Mapping and UV Coordinates': 'Texture coordinates, bilinear filtering, mipmapping, texture wrapping.'
            },
            learningOutcomes: [
              'Implement 3D rendering algorithms',
              'Apply lighting and shading models',
              'Create realistic 3D graphics with textures'
            ],
            contactHours: 18,
            practicalHours: 12,
            assignments: [
              '3D renderer implementation',
              'Ray tracer development',
              'Texture mapping system'
            ],
            practicalExercises: [
              'OpenGL 3D programming',
              'Shader programming with GLSL',
              'Real-time rendering techniques'
            ],
            caseStudies: [
              'Pixar RenderMan ray tracing',
              'Unity 3D rendering pipeline'
            ]
          }
        ]
      },
      'Cybersecurity': {
        units: [
          {
            title: 'Cryptography and Network Security',
            topics: [
              'AES (Advanced Encryption Standard)',
              'RSA Public Key Cryptography',
              'SHA-256 Hash Function',
              'Digital Signatures with DSA',
              'SSL/TLS Protocol Implementation'
            ],
            topicDescriptions: {
              'AES (Advanced Encryption Standard)': 'Symmetric encryption, 128/192/256-bit keys, SubBytes, ShiftRows, MixColumns operations.',
              'RSA Public Key Cryptography': 'Key generation, modular exponentiation, digital signatures, key exchange.',
              'SHA-256 Hash Function': 'Cryptographic hashing, message digest, collision resistance, blockchain applications.',
              'Digital Signatures with DSA': 'Digital Signature Algorithm, authentication, non-repudiation, signature verification.',
              'SSL/TLS Protocol Implementation': 'Handshake protocol, certificate validation, cipher suites, perfect forward secrecy.'
            },
            learningOutcomes: [
              'Implement cryptographic algorithms for data protection',
              'Design secure communication protocols',
              'Apply digital signatures for authentication'
            ],
            contactHours: 16,
            practicalHours: 10,
            assignments: [
              'Cryptographic library implementation',
              'Secure chat application',
              'Digital certificate system'
            ],
            practicalExercises: [
              'AES encryption/decryption implementation',
              'RSA key generation and usage',
              'SSL certificate configuration'
            ],
            caseStudies: [
              'WhatsApp end-to-end encryption',
              'Bitcoin blockchain cryptography'
            ]
          },
          {
            title: 'Ethical Hacking and Penetration Testing',
            topics: [
              'SQL Injection Attack Techniques',
              'Cross-Site Scripting (XSS)',
              'Buffer Overflow Exploitation',
              'Metasploit Framework Usage',
              'Nmap Network Scanning'
            ],
            topicDescriptions: {
              'SQL Injection Attack Techniques': 'Union-based, blind, time-based SQL injection, parameterized queries, input validation.',
              'Cross-Site Scripting (XSS)': 'Reflected, stored, DOM-based XSS, payload crafting, content security policy.',
              'Buffer Overflow Exploitation': 'Stack-based overflows, shellcode injection, NOP sleds, return address overwriting.',
              'Metasploit Framework Usage': 'Exploit modules, payload generation, post-exploitation, meterpreter sessions.',
              'Nmap Network Scanning': 'Port scanning techniques, service detection, OS fingerprinting, stealth scanning.'
            },
            learningOutcomes: [
              'Identify and exploit common web vulnerabilities',
              'Conduct professional penetration testing',
              'Use security tools for vulnerability assessment'
            ],
            contactHours: 18,
            practicalHours: 12,
            assignments: [
              'Web application security assessment',
              'Network penetration testing',
              'Vulnerability research project'
            ],
            practicalExercises: [
              'DVWA (Damn Vulnerable Web App) testing',
              'Metasploit exploitation scenarios',
              'Nmap scanning and enumeration'
            ],
            caseStudies: [
              'Equifax data breach analysis',
              'Target payment system compromise'
            ]
          }
        ]
      },
      'Mobile App Development': {
        units: [
          {
            title: 'Android Development with Java/Kotlin',
            topics: [
              'Android Activity Lifecycle',
              'RecyclerView and Adapter Pattern',
              'SQLite Database with Room ORM',
              'Retrofit HTTP Client Library',
              'Firebase Authentication Integration'
            ],
            topicDescriptions: {
              'Android Activity Lifecycle': 'onCreate, onStart, onResume, onPause, onStop, onDestroy methods, state management.',
              'RecyclerView and Adapter Pattern': 'ViewHolder pattern, data binding, item animations, performance optimization.',
              'SQLite Database with Room ORM': 'Entity classes, DAO interfaces, database migrations, type converters.',
              'Retrofit HTTP Client Library': 'REST API consumption, JSON parsing with Gson, async calls, error handling.',
              'Firebase Authentication Integration': 'Email/password auth, Google Sign-In, phone authentication, user management.'
            },
            learningOutcomes: [
              'Develop native Android applications',
              'Implement data persistence with Room database',
              'Integrate cloud services and APIs'
            ],
            contactHours: 16,
            practicalHours: 12,
            assignments: [
              'Todo app with local database',
              'Weather app with API integration',
              'Social media app with authentication'
            ],
            practicalExercises: [
              'Android Studio project development',
              'Material Design implementation',
              'Firebase backend integration'
            ],
            caseStudies: [
              'Instagram Android architecture',
              'Uber driver app development'
            ]
          },
          {
            title: 'iOS Development with Swift',
            topics: [
              'Swift Programming Language Features',
              'UIKit Framework and Storyboards',
              'Core Data Framework',
              'URLSession for Network Requests',
              'Push Notifications with APNs'
            ],
            topicDescriptions: {
              'Swift Programming Language Features': 'Optionals, closures, protocols, extensions, generics, memory management.',
              'UIKit Framework and Storyboards': 'View controllers, Auto Layout, segues, table views, collection views.',
              'Core Data Framework': 'Managed object context, persistent store, data modeling, fetch requests.',
              'URLSession for Network Requests': 'HTTP requests, JSON parsing, async/await, error handling.',
              'Push Notifications with APNs': 'Apple Push Notification service, device tokens, notification payloads.'
            },
            learningOutcomes: [
              'Build native iOS applications using Swift',
              'Implement data persistence with Core Data',
              'Handle network communication and push notifications'
            ],
            contactHours: 16,
            practicalHours: 12,
            assignments: [
              'iOS calculator app',
              'News reader with API',
              'Chat app with notifications'
            ],
            practicalExercises: [
              'Xcode development environment',
              'Interface Builder usage',
              'App Store submission process'
            ],
            caseStudies: [
              'Airbnb iOS app architecture',
              'Spotify iOS development practices'
            ]
          }
        ]
      },
      'Cloud Computing': {
        units: [
          {
            title: 'Amazon Web Services (AWS) Fundamentals',
            topics: [
              'EC2 Instance Management',
              'S3 Bucket Storage Operations',
              'RDS Database Services',
              'Lambda Serverless Functions',
              'CloudFormation Infrastructure as Code'
            ],
            topicDescriptions: {
              'EC2 Instance Management': 'Virtual machine provisioning, instance types, security groups, key pairs, auto-scaling.',
              'S3 Bucket Storage Operations': 'Object storage, bucket policies, versioning, lifecycle management, static website hosting.',
              'RDS Database Services': 'Managed databases, MySQL/PostgreSQL, automated backups, read replicas, multi-AZ deployment.',
              'Lambda Serverless Functions': 'Event-driven computing, function triggers, API Gateway integration, cold starts.',
              'CloudFormation Infrastructure as Code': 'Template-based provisioning, stack management, resource dependencies, rollback.'
            },
            learningOutcomes: [
              'Deploy and manage cloud infrastructure on AWS',
              'Implement serverless architectures',
              'Automate infrastructure provisioning'
            ],
            contactHours: 15,
            practicalHours: 10,
            assignments: [
              'Web application deployment on AWS',
              'Serverless API development',
              'Infrastructure automation project'
            ],
            practicalExercises: [
              'EC2 instance configuration',
              'Lambda function development',
              'CloudFormation template creation'
            ],
            caseStudies: [
              'Netflix AWS migration',
              'Airbnb cloud architecture'
            ]
          },
          {
            title: 'Docker Containerization and Kubernetes',
            topics: [
              'Docker Container Creation',
              'Dockerfile Best Practices',
              'Kubernetes Pod Management',
              'Kubernetes Service Discovery',
              'Helm Chart Deployment'
            ],
            topicDescriptions: {
              'Docker Container Creation': 'Image building, container lifecycle, volume mounting, network configuration.',
              'Dockerfile Best Practices': 'Multi-stage builds, layer optimization, security scanning, base image selection.',
              'Kubernetes Pod Management': 'Pod specifications, resource limits, health checks, restart policies.',
              'Kubernetes Service Discovery': 'ClusterIP, NodePort, LoadBalancer services, ingress controllers.',
              'Helm Chart Deployment': 'Package management, templating, release management, chart repositories.'
            },
            learningOutcomes: [
              'Containerize applications using Docker',
              'Orchestrate containers with Kubernetes',
              'Manage application deployments with Helm'
            ],
            contactHours: 18,
            practicalHours: 12,
            assignments: [
              'Microservices containerization',
              'Kubernetes cluster deployment',
              'CI/CD pipeline with containers'
            ],
            practicalExercises: [
              'Docker image optimization',
              'Kubernetes YAML manifests',
              'Helm chart development'
            ],
            caseStudies: [
              'Google Kubernetes Engine usage',
              'Spotify container orchestration'
            ]
          }
        ]
      }
    };

    return contentMap[normalizedSubject] || {
      units: [
        {
          title: 'Fundamentals and Core Concepts',
          topics: [`Introduction to ${subject}`, `Basic Principles`, `Theoretical Foundations`, `Practical Applications`, `Current Trends`],
          topicDescriptions: {
            [`Introduction to ${subject}`]: `Comprehensive overview of ${subject} including history, applications, and current trends.`,
            'Basic Principles': `Fundamental principles and methodologies underlying ${subject}.`,
            'Theoretical Foundations': `Mathematical and theoretical concepts essential for understanding ${subject}.`,
            'Practical Applications': `Real-world applications and use cases of ${subject} in industry.`,
            'Current Trends': `Latest developments and emerging trends in ${subject}.`
          },
          learningOutcomes: [
            `Understand the fundamental concepts of ${subject}`,
            `Apply basic principles to solve problems`,
            `Analyze theoretical foundations`
          ],
          contactHours: 12,
          practicalHours: 4,
          assignments: [`${subject} fundamentals assignment`, `Theoretical problem solving`],
          practicalExercises: [`Hands-on ${subject} exercises`],
          caseStudies: [`Real-world ${subject} applications`]
        }
      ]
    };
  }

  private generateDefaultUnit(subject: string, unitNumber: number) {
    const unitTitles = [
      'Fundamentals and Core Concepts',
      'Advanced Principles and Methodologies',
      'Practical Applications and Implementation',
      'Advanced Topics and Techniques',
      'Industry Applications and Future Trends'
    ];

    const baseTopics = [
      [`Introduction to ${subject}`, `Basic Principles`, `Theoretical Foundations`, `Practical Applications`, `Current Trends`],
      [`Advanced Concepts`, `Design Patterns`, `Best Practices`, `Performance Optimization`, `Quality Assurance`],
      [`Implementation Strategies`, `Tools and Technologies`, `Project Management`, `Testing and Validation`, `Deployment`],
      [`Advanced Algorithms`, `Optimization Techniques`, `Security Considerations`, `Scalability Issues`, `Integration`],
      [`Industry Standards`, `Emerging Technologies`, `Future Directions`, `Research Trends`, `Career Opportunities`]
    ];

    const topicIndex = (unitNumber - 1) % baseTopics.length;
    const topics = baseTopics[topicIndex];

    return {
      title: unitTitles[topicIndex] || `Unit ${unitNumber}: Advanced Topics`,
      topics: topics,
      topicDescriptions: topics.reduce((acc: { [key: string]: string }, topic) => {
        acc[topic] = `Comprehensive coverage of ${topic.toLowerCase()} in the context of ${subject}.`;
        return acc;
      }, {}),
      learningOutcomes: [
        `Understand and apply ${topics[0].toLowerCase()}`,
        `Implement ${topics[1].toLowerCase()} effectively`,
        `Analyze ${topics[2].toLowerCase()} in real-world scenarios`
      ],
      contactHours: 12,
      practicalHours: 4,
      assignments: [
        `${topics[0]} analysis assignment`,
        `${topics[1]} implementation project`
      ],
      practicalExercises: [
        `Hands-on ${topics[2]} exercises`,
        `${topics[3]} practical implementation`
      ],
      caseStudies: [
        `Industry case study on ${topics[4]}`,
        `Real-world application of ${subject}`
      ]
    };
  }
}
