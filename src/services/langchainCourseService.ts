import { Course, CourseGenerationRequest } from '../types/course';
import { LLMConfig } from './llmService';

// LangChain imports for advanced course generation
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { BaseLanguageModel } from '@langchain/core/language_models/base';
import { PromptTemplate } from '@langchain/core/prompts';
import { LLMChain } from 'langchain/chains';
import { AgentExecutor, createReactAgent } from 'langchain/agents';
import { Tool } from '@langchain/core/tools';
import { z } from 'zod';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { DynamicTool } from '@langchain/core/tools';

// Custom tools for course generation
class IndustryResearchTool extends Tool {
  name = 'industry_research';
  description = 'Research current industry trends and requirements for a given subject area';

  async _call(subject: string): Promise<string> {
    // Simulate industry research - in a real implementation, this could call external APIs
    const industryData = {
      'computer science': {
        trends: ['AI/ML', 'Cloud Computing', 'DevOps', 'Cybersecurity', 'Full-stack Development'],
        skills: ['Python', 'JavaScript', 'Docker', 'Kubernetes', 'React', 'Node.js'],
        certifications: ['AWS Certified', 'Google Cloud Professional', 'Microsoft Azure', 'Certified Kubernetes Administrator'],
        salaryRange: '₹6-25 LPA',
        jobGrowth: '22% (Much faster than average)'
      },
      'data science': {
        trends: ['MLOps', 'AutoML', 'Edge AI', 'Explainable AI', 'Real-time Analytics'],
        skills: ['Python', 'R', 'SQL', 'TensorFlow', 'PyTorch', 'Tableau', 'Power BI'],
        certifications: ['Google Data Analytics', 'IBM Data Science', 'Microsoft Azure Data Scientist'],
        salaryRange: '₹8-30 LPA',
        jobGrowth: '35% (Much faster than average)'
      },
      'artificial intelligence': {
        trends: ['Generative AI', 'Computer Vision', 'NLP', 'Robotics', 'Autonomous Systems'],
        skills: ['Python', 'TensorFlow', 'PyTorch', 'OpenCV', 'NLTK', 'Transformers'],
        certifications: ['TensorFlow Developer', 'PyTorch Certified', 'NVIDIA Deep Learning'],
        salaryRange: '₹10-40 LPA',
        jobGrowth: '40% (Much faster than average)'
      }
    };

    const subjectKey = subject.toLowerCase();
    const data = industryData[subjectKey] || industryData['computer science'];
    
    return JSON.stringify({
      subject,
      currentTrends: data.trends,
      requiredSkills: data.skills,
      relevantCertifications: data.certifications,
      marketInfo: {
        salaryRange: data.salaryRange,
        jobGrowth: data.jobGrowth
      },
      lastUpdated: new Date().toISOString()
    });
  }
}

class AICTEComplianceTool extends Tool {
  name = 'aicte_compliance';
  description = 'Check AICTE compliance requirements for course structure and content';

  async _call(courseType: string): Promise<string> {
    const complianceData = {
      undergraduate: {
        minCredits: 160,
        maxCreditsPerSemester: 26,
        minContactHours: 15,
        assessmentPattern: { internal: 40, external: 60 },
        mandatoryComponents: ['Theory', 'Practical', 'Project Work', 'Internship'],
        facultyRequirements: 'PhD or M.Tech with 2+ years industry experience'
      },
      postgraduate: {
        minCredits: 80,
        maxCreditsPerSemester: 24,
        minContactHours: 15,
        assessmentPattern: { internal: 40, external: 60 },
        mandatoryComponents: ['Theory', 'Practical', 'Research Project', 'Dissertation'],
        facultyRequirements: 'PhD with research publications'
      }
    };

    const data = complianceData[courseType.toLowerCase()] || complianceData.undergraduate;
    
    return JSON.stringify({
      courseType,
      creditRequirements: data.minCredits,
      semesterLimits: data.maxCreditsPerSemester,
      contactHours: data.minContactHours,
      assessmentPattern: data.assessmentPattern,
      mandatoryComponents: data.mandatoryComponents,
      facultyRequirements: data.facultyRequirements,
      complianceVersion: 'AICTE 2023-24'
    });
  }
}

class BloomsTaxonomyTool extends Tool {
  name = 'blooms_taxonomy';
  description = 'Generate learning outcomes aligned with Bloom\'s Taxonomy levels';

  async _call(input: string): Promise<string> {
    const { subject, level } = JSON.parse(input);
    
    const bloomsLevels = {
      remember: ['recall', 'recognize', 'list', 'identify', 'name', 'state'],
      understand: ['explain', 'describe', 'summarize', 'interpret', 'classify', 'compare'],
      apply: ['implement', 'execute', 'use', 'demonstrate', 'solve', 'operate'],
      analyze: ['differentiate', 'organize', 'attribute', 'deconstruct', 'examine', 'break down'],
      evaluate: ['critique', 'judge', 'assess', 'defend', 'justify', 'validate'],
      create: ['design', 'construct', 'develop', 'formulate', 'produce', 'generate']
    };

    const outcomes = [];
    Object.entries(bloomsLevels).forEach(([bloomLevel, verbs]) => {
      const verb = verbs[Math.floor(Math.random() * verbs.length)];
      outcomes.push({
        level: bloomLevel,
        verb,
        outcome: `Students will be able to ${verb} key concepts and principles of ${subject}`
      });
    });

    return JSON.stringify({
      subject,
      academicLevel: level,
      bloomsOutcomes: outcomes,
      distributionRecommendation: {
        foundational: ['remember', 'understand'],
        intermediate: ['apply', 'analyze'],
        advanced: ['evaluate', 'create']
      }
    });
  }
}

class SyllabusResearchTool extends Tool {
  name = 'syllabus_research';
  description = 'Research syllabus content from top institutes like IITs, IISc for any subject';

  async _call(subject: string): Promise<string> {
    // Simulate web search for syllabus content from top institutes
    // In a real implementation, this would use web search APIs or scraping

    const topInstitutes = ['IIT Delhi', 'IIT Bombay', 'IIT Madras', 'IISc Bangalore', 'IIT Kanpur', 'IIT Kharagpur'];

    // Comprehensive syllabus database for various subjects
    const syllabusDatabase = {
      'machine learning': {
        units: [
          {
            title: 'Mathematical Foundations and Introduction',
            topics: [
              'Linear Algebra: Vectors, Matrices, Eigenvalues, SVD',
              'Probability Theory: Bayes Theorem, Distributions',
              'Statistics: Hypothesis Testing, Confidence Intervals',
              'Optimization: Gradient Descent, Lagrange Multipliers',
              'Information Theory: Entropy, Mutual Information'
            ]
          },
          {
            title: 'Supervised Learning Algorithms',
            topics: [
              'Linear Regression: OLS, Ridge, Lasso Regression',
              'Logistic Regression: Binary and Multiclass Classification',
              'Decision Trees: ID3, C4.5, CART, Pruning Techniques',
              'Support Vector Machines: Linear and Non-linear SVM, Kernel Trick',
              'Naive Bayes: Gaussian, Multinomial, Bernoulli NB'
            ]
          },
          {
            title: 'Unsupervised Learning and Dimensionality Reduction',
            topics: [
              'K-Means Clustering: Lloyd\'s Algorithm, K-means++',
              'Hierarchical Clustering: Agglomerative, Divisive Methods',
              'DBSCAN: Density-based Clustering, Parameter Selection',
              'Principal Component Analysis: Eigenvalue Decomposition',
              'Independent Component Analysis: FastICA Algorithm'
            ]
          },
          {
            title: 'Neural Networks and Deep Learning',
            topics: [
              'Perceptron: Single and Multi-layer Perceptrons',
              'Backpropagation: Chain Rule, Gradient Computation',
              'Convolutional Neural Networks: Conv Layers, Pooling',
              'Recurrent Neural Networks: LSTM, GRU, Attention',
              'Regularization: Dropout, Batch Normalization, Weight Decay'
            ]
          },
          {
            title: 'Advanced Topics and Applications',
            topics: [
              'Ensemble Methods: Bagging, Boosting, Random Forest',
              'Reinforcement Learning: Q-Learning, Policy Gradients',
              'Natural Language Processing: Word Embeddings, Transformers',
              'Computer Vision: Object Detection, Image Segmentation',
              'Model Evaluation: Cross-validation, ROC, Precision-Recall'
            ]
          }
        ],
        textbooks: [
          'Pattern Recognition and Machine Learning - Christopher Bishop',
          'The Elements of Statistical Learning - Hastie, Tibshirani, Friedman',
          'Machine Learning: A Probabilistic Perspective - Kevin Murphy'
        ],
        references: [
          'Hands-On Machine Learning - Aurélien Géron',
          'Deep Learning - Ian Goodfellow, Yoshua Bengio, Aaron Courville',
          'Python Machine Learning - Sebastian Raschka'
        ]
      },
      'operating systems': {
        units: [
          {
            title: 'Introduction to Operating Systems',
            topics: [
              'OS Structure: Monolithic, Microkernel, Hybrid Architectures',
              'System Calls: Process, File, Device, Information Maintenance',
              'OS Services: Program Execution, I/O Operations, File Systems',
              'User and Kernel Modes: Privilege Levels, Mode Switching',
              'Interrupts and Exceptions: Hardware and Software Interrupts'
            ]
          },
          {
            title: 'Process Management',
            topics: [
              'Process Concept: PCB, Process States, Process Creation',
              'CPU Scheduling: FCFS, SJF, Priority, Round Robin',
              'Process Synchronization: Critical Section, Semaphores',
              'Deadlocks: Detection, Prevention, Avoidance, Recovery',
              'Inter-process Communication: Pipes, Message Queues, Shared Memory'
            ]
          },
          {
            title: 'Memory Management',
            topics: [
              'Memory Hierarchy: Cache, Main Memory, Secondary Storage',
              'Contiguous Allocation: Fixed and Variable Partitioning',
              'Paging: Page Tables, TLB, Multi-level Paging',
              'Virtual Memory: Demand Paging, Page Replacement Algorithms',
              'Segmentation: Segment Tables, Segmentation with Paging'
            ]
          },
          {
            title: 'File Systems and I/O',
            topics: [
              'File System Interface: File Operations, Directory Structure',
              'File System Implementation: Allocation Methods, Free Space',
              'Mass Storage: Disk Scheduling, RAID, SSD Technology',
              'I/O Systems: Hardware, Application Interface, Kernel I/O',
              'Protection and Security: Access Control, Authentication'
            ]
          },
          {
            title: 'Advanced Topics',
            topics: [
              'Distributed Systems: Network OS, Distributed File Systems',
              'Real-time Systems: Hard and Soft Real-time, Scheduling',
              'Virtualization: Hypervisors, Container Technology',
              'Mobile Operating Systems: Android, iOS Architecture',
              'Security: Buffer Overflow, Access Control, Cryptography'
            ]
          }
        ],
        textbooks: [
          'Operating System Concepts - Silberschatz, Galvin, Gagne',
          'Modern Operating Systems - Andrew Tanenbaum',
          'Operating Systems: Three Easy Pieces - Remzi Arpaci-Dusseau'
        ],
        references: [
          'The Design and Implementation of the FreeBSD Operating System',
          'Linux Kernel Development - Robert Love',
          'Understanding the Linux Kernel - Daniel Bovet'
        ]
      },
      'computer networks': {
        units: [
          {
            title: 'Network Fundamentals and Architecture',
            topics: [
              'Network Types: LAN, WAN, MAN, PAN, Network Topologies',
              'OSI Reference Model: Seven Layers, Protocol Stack',
              'TCP/IP Model: Application, Transport, Internet, Link Layers',
              'Network Performance: Bandwidth, Latency, Throughput, Jitter',
              'Network Standards: IEEE 802, ITU-T, IETF Standards'
            ]
          },
          {
            title: 'Physical and Data Link Layer',
            topics: [
              'Transmission Media: Guided and Unguided Media',
              'Digital Transmission: Line Coding, Block Coding',
              'Error Detection and Correction: Parity, CRC, Hamming Code',
              'Data Link Protocols: HDLC, PPP, Ethernet',
              'Multiple Access: ALOHA, CSMA/CD, Token Ring'
            ]
          },
          {
            title: 'Network Layer and Routing',
            topics: [
              'IP Addressing: IPv4, IPv6, Subnetting, CIDR',
              'Routing Algorithms: Distance Vector, Link State',
              'Routing Protocols: RIP, OSPF, BGP',
              'Network Address Translation: NAT, PAT',
              'Internet Control Protocols: ICMP, ARP, DHCP'
            ]
          },
          {
            title: 'Transport Layer Protocols',
            topics: [
              'UDP: Connectionless Service, UDP Header Format',
              'TCP: Connection-oriented Service, Three-way Handshake',
              'Flow Control: Stop-and-wait, Sliding Window',
              'Congestion Control: Slow Start, Congestion Avoidance',
              'Quality of Service: Traffic Shaping, Packet Scheduling'
            ]
          },
          {
            title: 'Application Layer and Network Security',
            topics: [
              'DNS: Domain Name System, DNS Resolution Process',
              'HTTP/HTTPS: Web Protocols, REST APIs',
              'Email Protocols: SMTP, POP3, IMAP',
              'Network Security: Cryptography, Firewalls, VPN',
              'Wireless Networks: WiFi, Bluetooth, Cellular Networks'
            ]
          }
        ],
        textbooks: [
          'Computer Networking: A Top-Down Approach - Kurose, Ross',
          'Computer Networks - Andrew Tanenbaum',
          'TCP/IP Illustrated - W. Richard Stevens'
        ],
        references: [
          'Network+ Guide to Networks - Tamara Dean',
          'Internetworking with TCP/IP - Douglas Comer',
          'Computer Networks and Internets - Douglas Comer'
        ]
      },
      'data structures': {
        units: [
          {
            title: 'Introduction and Array-based Structures',
            topics: [
              'Abstract Data Types: Interface vs Implementation',
              'Algorithm Analysis: Big O, Omega, Theta Notation',
              'Arrays: Static and Dynamic Arrays, Multi-dimensional Arrays',
              'Strings: String Matching Algorithms, KMP, Boyer-Moore',
              'Sparse Matrices: Representation and Operations'
            ]
          },
          {
            title: 'Linear Data Structures',
            topics: [
              'Linked Lists: Singly, Doubly, Circular Linked Lists',
              'Stacks: Array and Linked Implementation, Applications',
              'Queues: Linear, Circular, Priority Queues, Deque',
              'Expression Evaluation: Infix, Prefix, Postfix Conversion',
              'Recursion: Direct, Indirect, Tail Recursion, Stack Frames'
            ]
          },
          {
            title: 'Trees and Hierarchical Structures',
            topics: [
              'Binary Trees: Traversals, Threaded Binary Trees',
              'Binary Search Trees: Insertion, Deletion, Search Operations',
              'AVL Trees: Rotation Operations, Height Balancing',
              'B-Trees and B+ Trees: Multi-way Search Trees',
              'Heap Data Structure: Min-Heap, Max-Heap, Heap Sort'
            ]
          },
          {
            title: 'Graphs and Advanced Structures',
            topics: [
              'Graph Representation: Adjacency Matrix, Adjacency List',
              'Graph Traversals: Depth-First Search, Breadth-First Search',
              'Shortest Path Algorithms: Dijkstra, Bellman-Ford, Floyd-Warshall',
              'Minimum Spanning Tree: Kruskal, Prim Algorithms',
              'Topological Sorting: Kahn Algorithm, DFS-based Approach'
            ]
          },
          {
            title: 'Hashing and Advanced Topics',
            topics: [
              'Hash Tables: Hash Functions, Collision Resolution',
              'Open Addressing: Linear, Quadratic, Double Hashing',
              'Separate Chaining: Implementation and Analysis',
              'Tries: Prefix Trees, Compressed Tries, Applications',
              'Disjoint Set Union: Union-Find, Path Compression'
            ]
          }
        ],
        textbooks: [
          'Introduction to Algorithms - Cormen, Leiserson, Rivest, Stein',
          'Data Structures and Algorithms in Java - Robert Lafore',
          'Algorithms - Robert Sedgewick, Kevin Wayne'
        ],
        references: [
          'Data Structures and Algorithm Analysis in C++ - Mark Allen Weiss',
          'Cracking the Coding Interview - Gayle Laakmann McDowell',
          'Algorithm Design Manual - Steven Skiena'
        ]
      },
      'database management systems': {
        units: [
          {
            title: 'Database Fundamentals and ER Modeling',
            topics: [
              'Database System Architecture: Three-level Architecture',
              'Data Models: Hierarchical, Network, Relational, Object-oriented',
              'Entity-Relationship Model: Entities, Attributes, Relationships',
              'ER Diagrams: Weak Entities, Aggregation, Generalization',
              'Enhanced ER Model: Specialization, Inheritance, Categories'
            ]
          },
          {
            title: 'Relational Model and SQL',
            topics: [
              'Relational Model: Relations, Tuples, Domains, Keys',
              'Relational Algebra: Selection, Projection, Join Operations',
              'SQL Fundamentals: DDL, DML, DCL, TCL Commands',
              'Advanced SQL: Subqueries, Views, Stored Procedures, Triggers',
              'Aggregate Functions: GROUP BY, HAVING, Window Functions'
            ]
          },
          {
            title: 'Database Design and Normalization',
            topics: [
              'Functional Dependencies: Armstrong Axioms, Closure',
              'Normalization: 1NF, 2NF, 3NF, BCNF',
              'Multivalued Dependencies: 4NF, 5NF',
              'Database Design Process: Conceptual, Logical, Physical Design',
              'Denormalization: When and Why to Denormalize'
            ]
          },
          {
            title: 'Transaction Management and Concurrency',
            topics: [
              'Transaction Concepts: ACID Properties, Transaction States',
              'Concurrency Control: Lock-based, Timestamp-based Protocols',
              'Deadlock Handling: Detection, Prevention, Recovery',
              'Recovery Techniques: Log-based, Shadow Paging',
              'Isolation Levels: Read Uncommitted, Read Committed, Serializable'
            ]
          },
          {
            title: 'Advanced Database Concepts',
            topics: [
              'Indexing: B+ Tree, Hash Indexing, Bitmap Indexing',
              'Query Optimization: Cost-based, Rule-based Optimization',
              'Distributed Databases: Fragmentation, Replication, CAP Theorem',
              'NoSQL Databases: Document, Key-value, Column-family, Graph',
              'Data Warehousing: OLAP, Data Mining, ETL Processes'
            ]
          }
        ],
        textbooks: [
          'Database System Concepts - Silberschatz, Korth, Sudarshan',
          'Fundamentals of Database Systems - Elmasri, Navathe',
          'Database Management Systems - Raghu Ramakrishnan'
        ],
        references: [
          'SQL in 10 Minutes - Ben Forta',
          'Learning SQL - Alan Beaulieu',
          'High Performance MySQL - Baron Schwartz'
        ]
      },
      'software engineering': {
        units: [
          {
            title: 'Software Engineering Fundamentals',
            topics: [
              'Software Engineering Principles: Modularity, Abstraction, Encapsulation',
              'Software Development Life Cycle: Waterfall, Iterative, Spiral',
              'Agile Methodologies: Scrum, Kanban, Extreme Programming',
              'Software Process Models: Rational Unified Process, DevOps',
              'Software Engineering Ethics: Professional Responsibility, Codes of Conduct'
            ]
          },
          {
            title: 'Requirements Engineering',
            topics: [
              'Requirements Elicitation: Interviews, Surveys, Observation',
              'Requirements Analysis: Functional, Non-functional Requirements',
              'Requirements Specification: SRS, Use Cases, User Stories',
              'Requirements Validation: Reviews, Prototyping, Testing',
              'Requirements Management: Traceability, Change Control'
            ]
          },
          {
            title: 'Software Design and Architecture',
            topics: [
              'Software Design Principles: Coupling, Cohesion, SOLID Principles',
              'Architectural Patterns: Layered, MVC, Microservices, SOA',
              'Design Patterns: Creational, Structural, Behavioral Patterns',
              'UML Diagrams: Class, Sequence, Activity, State Diagrams',
              'Software Architecture: Quality Attributes, Architecture Evaluation'
            ]
          },
          {
            title: 'Implementation and Testing',
            topics: [
              'Coding Standards: Naming Conventions, Documentation, Code Reviews',
              'Version Control: Git, Branching Strategies, Merge Conflicts',
              'Testing Fundamentals: Unit, Integration, System, Acceptance Testing',
              'Test-Driven Development: Red-Green-Refactor Cycle',
              'Automated Testing: Test Frameworks, Continuous Integration'
            ]
          },
          {
            title: 'Maintenance and Project Management',
            topics: [
              'Software Maintenance: Corrective, Adaptive, Perfective, Preventive',
              'Software Metrics: LOC, Cyclomatic Complexity, Function Points',
              'Project Planning: Work Breakdown Structure, Estimation Techniques',
              'Risk Management: Risk Identification, Assessment, Mitigation',
              'Software Quality Assurance: Quality Models, Process Improvement'
            ]
          }
        ],
        textbooks: [
          'Software Engineering: A Practitioner\'s Approach - Roger Pressman',
          'Clean Code - Robert C. Martin',
          'Design Patterns - Gang of Four'
        ],
        references: [
          'The Pragmatic Programmer - David Thomas, Andrew Hunt',
          'Code Complete - Steve McConnell',
          'Refactoring - Martin Fowler'
        ]
      }
    };

    const normalizedSubject = subject.toLowerCase().replace(/[^a-z\s]/g, '').trim();
    const syllabusData = syllabusDatabase[normalizedSubject];

    if (syllabusData) {
      return JSON.stringify({
        subject: subject,
        source: `Compiled from ${topInstitutes.join(', ')} syllabi`,
        units: syllabusData.units,
        recommendedTextbooks: syllabusData.textbooks,
        referenceBooks: syllabusData.references,
        lastUpdated: new Date().toISOString(),
        confidence: 'high'
      });
    } else {
      // For unknown subjects, provide a framework for research
      return JSON.stringify({
        subject: subject,
        source: 'General academic framework',
        researchNeeded: true,
        suggestedApproach: [
          `Search for "${subject}" syllabus from IIT Delhi, IIT Bombay`,
          `Look for course content from IISc Bangalore`,
          `Check international university curricula for ${subject}`,
          `Review industry requirements for ${subject} professionals`
        ],
        confidence: 'low',
        fallbackUnits: [
          {
            title: `Fundamentals of ${subject}`,
            topics: [`Introduction to ${subject}`, 'Historical Development', 'Basic Principles', 'Core Concepts', 'Applications']
          },
          {
            title: `Theoretical Foundations`,
            topics: ['Mathematical Models', 'Theoretical Framework', 'Key Algorithms', 'Design Principles', 'Analysis Methods']
          },
          {
            title: `Practical Applications`,
            topics: ['Implementation Techniques', 'Case Studies', 'Industry Applications', 'Tools and Technologies', 'Best Practices']
          },
          {
            title: `Advanced Topics`,
            topics: ['Recent Developments', 'Research Areas', 'Emerging Trends', 'Future Directions', 'Specialized Applications']
          },
          {
            title: `Project and Assessment`,
            topics: ['Project Work', 'Laboratory Exercises', 'Performance Evaluation', 'Quality Metrics', 'Professional Skills']
          }
        ]
      });
    }
  }
}

export class LangChainCourseService {
  private llm: BaseLanguageModel;
  private config: LLMConfig;
  private tools: Tool[];
  private agent: AgentExecutor | null = null;

  constructor(config: LLMConfig) {
    this.config = config;
    this.llm = this.initializeLLM();
    this.tools = this.initializeTools();
    this.initializeAgent();
  }

  private initializeLLM(): BaseLanguageModel {
    const { provider, apiKey, model, temperature = 0.7, maxTokens = 4000 } = this.config;

    switch (provider) {
      case 'openai':
        return new ChatOpenAI({
          openAIApiKey: apiKey,
          modelName: model || 'gpt-4-turbo-preview',
          temperature,
          maxTokens,
        });
      
      case 'anthropic':
        return new ChatAnthropic({
          anthropicApiKey: apiKey,
          modelName: model || 'claude-3-sonnet-20240229',
          temperature,
          maxTokens,
        });
      
      case 'google':
        return new ChatGoogleGenerativeAI({
          apiKey: apiKey,
          modelName: model || 'gemini-pro',
          temperature,
          maxTokens,
        });
      
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  private initializeTools(): Tool[] {
    return [
      new IndustryResearchTool(),
      new AICTEComplianceTool(),
      new BloomsTaxonomyTool(),
      new SyllabusResearchTool(),
    ];
  }

  private async initializeAgent(): Promise<void> {
    try {
      const prompt = PromptTemplate.fromTemplate(`
You are an expert Course Design Agent specializing in Indian higher education. You have access to tools that can help you research industry trends, check AICTE compliance, and design learning outcomes.

Available tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
{agent_scratchpad}
      `);

      const agent = await createReactAgent({
        llm: this.llm,
        tools: this.tools,
        prompt,
      });

      this.agent = new AgentExecutor({
        agent,
        tools: this.tools,
        verbose: true,
        maxIterations: 5,
      });
    } catch (error) {
      console.error('Error initializing agent:', error);
      this.agent = null;
    }
  }

  async generateCourse(request: CourseGenerationRequest, userPreferences?: { cosCount: number; unitsCount: number }): Promise<Course> {
    try {
      console.log('🔍 Researching syllabus content from top institutes...');

      // Use the syllabus research tool to get real content
      const syllabusResearchTool = new SyllabusResearchTool();
      const syllabusData = await syllabusResearchTool._call(request.subject);
      const parsedSyllabusData = JSON.parse(syllabusData);

      console.log('📚 Syllabus research completed:', parsedSyllabusData.confidence);

      // Generate course using LLM with the researched content
      const coursePrompt = this.buildCoursePrompt(request, parsedSyllabusData, userPreferences);
      const courseContent = await this.llm.invoke(coursePrompt);

      // Parse and structure the course
      return this.buildCourseFromResearch(request, parsedSyllabusData, userPreferences);

    } catch (error) {
      console.error('Error in LangChain course generation:', error);
      throw new Error('Failed to generate course using LangChain. Please try again.');
    }
  }

  private buildCoursePrompt(request: CourseGenerationRequest, syllabusData: any, userPreferences?: { cosCount: number; unitsCount: number }): string {
    const cosCount = userPreferences?.cosCount || 5;
    const unitsCount = userPreferences?.unitsCount || 5;

    return `
You are an expert curriculum designer creating a comprehensive course syllabus for "${request.subject}".

RESEARCH DATA FROM TOP INSTITUTES:
${JSON.stringify(syllabusData, null, 2)}

COURSE REQUIREMENTS:
- Subject: ${request.subject}
- Academic Level: ${request.level || 'Undergraduate'}
- Semester: ${request.semester}
- Duration: ${request.duration} weeks
- Credits: ${request.credits}
- Department: ${request.department}
- Course Type: ${request.courseType}
- Number of Course Outcomes: ${cosCount}
- Number of Units: ${unitsCount}

INSTRUCTIONS:
1. Use the researched syllabus content from top institutes as the foundation
2. Create ${unitsCount} comprehensive units with specific, technical topics
3. Generate ${cosCount} course outcomes aligned with Bloom's taxonomy
4. Include industry-relevant skills and modern applications
5. Ensure AICTE compliance for Indian higher education
6. Provide detailed topic descriptions for each unit
7. Include practical exercises and case studies

Create a detailed, professional course syllabus that matches the quality of top Indian institutes.
    `;
  }

  private buildCourseFromResearch(request: CourseGenerationRequest, syllabusData: any, userPreferences?: { cosCount: number; unitsCount: number }): Course {
    const cosCount = userPreferences?.cosCount || 5;
    const unitsCount = userPreferences?.unitsCount || 5;

    // Use researched content or fallback
    const units = syllabusData.units || syllabusData.fallbackUnits || [];
    const selectedUnits = units.slice(0, unitsCount);

    // Generate course outcomes
    const outcomes = this.generateEnhancedOutcomes(request.subject, cosCount);

    // Build comprehensive units
    const courseUnits = selectedUnits.map((unit: any, index: number) => ({
      unitNumber: index + 1,
      title: unit.title,
      topics: unit.topics || [],
      topicDescriptions: this.generateTopicDescriptions(unit.topics || [], request.subject),
      learningOutcomes: this.generateUnitOutcomes(unit.title, request.subject),
      contactHours: 15,
      practicalHours: 6,
      assignments: this.generateAssignments(unit.topics || [], request.subject),
      practicalExercises: this.generatePracticalExercises(unit.topics || [], request.subject),
      caseStudies: this.generateCaseStudies(unit.title, request.subject)
    }));

    return {
      id: `course_${Date.now()}`,
      title: request.subject,
      code: `${request.department.substring(0, 2).toUpperCase()}${300 + request.semester}`,
      credits: request.credits || (request.courseType === 'practical' ? 2 : 4),
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: this.generatePrerequisites(request.subject, request.semester),
      objectives: this.generateObjectives(request.subject),
      outcomes: outcomes,
      units: courseUnits,
      textBooks: syllabusData.recommendedTextbooks || this.generateDefaultTextbooks(request.subject),
      referenceBooks: syllabusData.referenceBooks || this.generateDefaultReferences(request.subject),
      onlineResources: this.generateOnlineResources(request.subject),
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0,
        assignments: 10,
        quizzes: 10,
        midterm: 20
      },
      industryRelevance: this.generateIndustryRelevance(request.subject),
      marketDemand: 'High demand in current industry with significant growth expected',
      difficultyLevel: request.semester <= 4 ? 3 : 4,
      gradingScheme: 'Absolute grading as per university norms',
      courseDelivery: {
        lectureMethod: 'Interactive lectures with multimedia presentations and case studies',
        practicalMethod: 'Hands-on laboratory sessions with industry-standard tools',
        assessmentMethod: 'Continuous assessment with practical evaluations and projects'
      },
      createdAt: new Date(),
      generatedBy: `LangChain Enhanced (${this.config.provider.toUpperCase()}) - ${syllabusData.confidence} confidence`,
      syllabusSource: syllabusData.source || 'Enhanced research compilation'
    };
  }

  private parseAgentResponse(agentOutput: string, request: CourseGenerationRequest): Course {
    // This is a simplified parser - in a real implementation, you'd want more sophisticated parsing
    // For now, we'll create a basic course structure based on the agent's research
    
    return {
      id: `course_${Date.now()}`,
      title: `${request.subject}`,
      code: `${request.department.substring(0, 2).toUpperCase()}${300 + request.semester}`,
      credits: request.courseType === 'practical' ? 2 : 4,
      contactHours: {
        lecture: request.courseType === 'practical' ? 1 : 3,
        tutorial: 1,
        practical: request.courseType === 'practical' ? 4 : 2
      },
      prerequisites: [],
      objectives: [
        { id: 1, description: `Understand fundamental concepts of ${request.subject}` },
        { id: 2, description: `Apply theoretical knowledge to practical problems` },
        { id: 3, description: `Analyze and evaluate different approaches in ${request.subject}` },
      ],
      outcomes: [
        { id: 'CO1', description: `Explain core principles of ${request.subject}`, bloomsLevel: 'Understand' },
        { id: 'CO2', description: `Apply ${request.subject} concepts to solve problems`, bloomsLevel: 'Apply' },
        { id: 'CO3', description: `Analyze complex scenarios using ${request.subject}`, bloomsLevel: 'Analyze' },
      ],
      units: [], // Would be populated based on agent research
      textBooks: [],
      referenceBooks: [],
      onlineResources: [],
      assessmentPattern: {
        internalAssessment: 40,
        endSemExam: 60,
        practical: request.courseType === 'practical' ? 50 : 0
      },
      createdAt: new Date(),
      generatedBy: `LangChain Agent (${this.config.provider.toUpperCase()})`,
      agentResearch: agentOutput // Store the agent's research for reference
    };
  }

  private generateEnhancedOutcomes(subject: string, count: number) {
    const bloomsLevels = ['Remember', 'Understand', 'Apply', 'Analyze', 'Evaluate', 'Create'];
    const outcomes = [];

    for (let i = 1; i <= count; i++) {
      const bloomLevel = bloomsLevels[Math.min(i - 1, bloomsLevels.length - 1)];
      outcomes.push({
        id: `CO${i}`,
        description: this.getOutcomeDescription(subject, bloomLevel, i),
        bloomsLevel: bloomLevel,
        poMapping: [`PO${i}`, `PO${Math.min(i + 1, 12)}`]
      });
    }

    return outcomes;
  }

  private getOutcomeDescription(subject: string, bloomLevel: string, index: number): string {
    const descriptions = {
      'Remember': `Recall and identify fundamental concepts, principles, and terminology of ${subject}`,
      'Understand': `Explain and describe key theories, methods, and applications in ${subject}`,
      'Apply': `Implement and demonstrate practical skills and techniques in ${subject}`,
      'Analyze': `Analyze complex problems and evaluate different approaches in ${subject}`,
      'Evaluate': `Critically assess and justify solutions and methodologies in ${subject}`,
      'Create': `Design and develop innovative solutions and systems using ${subject} principles`
    };

    return descriptions[bloomLevel] || `Understand key concepts of ${subject}`;
  }

  private generateTopicDescriptions(topics: string[], subject: string) {
    const descriptions: { [key: string]: string } = {};
    topics.forEach(topic => {
      descriptions[topic] = `Comprehensive study of ${topic} including theoretical foundations, practical applications, and current industry practices in ${subject}.`;
    });
    return descriptions;
  }

  private generateUnitOutcomes(unitTitle: string, subject: string) {
    return [
      `Understand the fundamental concepts covered in ${unitTitle}`,
      `Apply the principles and techniques learned in practical scenarios`,
      `Analyze and evaluate different approaches within this unit's scope`
    ];
  }

  private generateAssignments(topics: string[], subject: string) {
    return topics.slice(0, 3).map(topic =>
      `${topic} analysis and implementation assignment`
    );
  }

  private generatePracticalExercises(topics: string[], subject: string) {
    return topics.slice(0, 4).map(topic =>
      `Hands-on laboratory exercise on ${topic}`
    );
  }

  private generateCaseStudies(unitTitle: string, subject: string) {
    return [
      `Industry case study related to ${unitTitle}`,
      `Real-world application analysis in ${subject}`
    ];
  }

  private generatePrerequisites(subject: string, semester: number): string[] {
    if (semester <= 2) return [];

    const prereqMap: { [key: string]: string[] } = {
      'machine learning': ['Data Structures', 'Statistics', 'Linear Algebra', 'Programming'],
      'operating systems': ['Computer Organization', 'Data Structures', 'Programming'],
      'computer networks': ['Operating Systems', 'Computer Organization', 'Mathematics'],
      'database management': ['Data Structures', 'Software Engineering', 'Programming'],
      'artificial intelligence': ['Data Structures', 'Statistics', 'Discrete Mathematics']
    };

    const key = subject.toLowerCase();
    return prereqMap[key] || ['Mathematics', 'Programming Fundamentals'];
  }

  private generateObjectives(subject: string) {
    return [
      { id: 1, description: `Understand fundamental concepts and principles of ${subject}` },
      { id: 2, description: `Apply theoretical knowledge to solve practical problems in ${subject}` },
      { id: 3, description: `Analyze different approaches and methodologies in ${subject}` },
      { id: 4, description: `Evaluate solutions and make informed decisions in ${subject} applications` },
      { id: 5, description: `Design and implement systems using ${subject} concepts` }
    ];
  }

  private generateDefaultTextbooks(subject: string): string[] {
    return [
      `Fundamentals of ${subject} - Academic Press`,
      `Advanced ${subject} - Technical Publications`,
      `Comprehensive Guide to ${subject} - Educational Publishers`
    ];
  }

  private generateDefaultReferences(subject: string): string[] {
    return [
      `${subject} Handbook - Reference Publications`,
      `Practical ${subject} - Industry Press`,
      `Modern Approaches to ${subject} - Research Publications`
    ];
  }

  private generateOnlineResources(subject: string): string[] {
    const encodedSubject = encodeURIComponent(subject);
    return [
      `https://www.coursera.org/search?query=${encodedSubject} - Coursera ${subject} Courses`,
      `https://www.edx.org/search?q=${encodedSubject} - edX ${subject} Courses`,
      `https://www.youtube.com/results?search_query=${encodedSubject}+tutorial - YouTube Tutorials`,
      `https://github.com/search?q=${encodedSubject} - GitHub Projects and Code`,
      `https://stackoverflow.com/questions/tagged/${subject.toLowerCase().replace(/\s+/g, '-')} - Stack Overflow Discussions`
    ];
  }

  private generateIndustryRelevance(subject: string) {
    return {
      skills: [`${subject} Programming`, 'Problem Solving', 'System Design', 'Technical Communication'],
      tools: ['Development Environment', 'Testing Tools', 'Version Control', 'Documentation Tools'],
      certifications: [`Professional ${subject} Certification`, 'Industry Standards Certification'],
      careerOpportunities: [`${subject} Specialist`, 'Technical Consultant', 'System Architect', 'Research Engineer']
    };
  }
}
