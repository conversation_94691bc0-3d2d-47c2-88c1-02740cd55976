// Browser-compatible imports - using simple hashing for demo
// import bcrypt from 'bcryptjs';
// import jwt from 'jsonwebtoken';
import { Course } from '../types/course';

// Database interface
export interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;
  department: string;
  institution: string;
  designation: string;
  createdAt: string;
  lastLogin?: string;
}

export interface UserCredentials {
  username: string;
  password: string;
}

export interface UserRegistration {
  username: string;
  email: string;
  password: string;
  fullName: string;
  department: string;
  institution: string;
  designation: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  token?: string;
}

export interface SavedCourse extends Course {
  userId: number;
  savedAt: string;
}

// Simple browser-compatible hash function
function simpleHash(password: string): string {
  let hash = 0;
  for (let i = 0; i < password.length; i++) {
    const char = password.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36) + '_' + btoa(password).slice(0, 10);
}

// Simple token generation
function generateToken(userId: number, username: string): string {
  const payload = { userId, username, exp: Date.now() + (7 * 24 * 60 * 60 * 1000) }; // 7 days
  return btoa(JSON.stringify(payload));
}

// Simple token verification
function verifyToken(token: string): { valid: boolean; userId?: number; username?: string } {
  try {
    const payload = JSON.parse(atob(token));
    if (payload.exp > Date.now()) {
      return { valid: true, userId: payload.userId, username: payload.username };
    }
    return { valid: false };
  } catch {
    return { valid: false };
  }
}

class DatabaseService {
  private JWT_SECRET = 'course-design-system-secret-key-2024';
  private STORAGE_KEYS = {
    USERS: 'course_system_users',
    COURSES: 'course_system_courses',
    PREFERENCES: 'course_system_preferences',
    NEXT_USER_ID: 'course_system_next_user_id',
    NEXT_COURSE_ID: 'course_system_next_course_id'
  };

  constructor() {
    console.log('Browser-compatible database service initialized');
    this.initializeStorage();
  }

  private initializeStorage(): void {
    // Initialize storage if not exists
    if (!localStorage.getItem(this.STORAGE_KEYS.USERS)) {
      localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify([]));
    }
    if (!localStorage.getItem(this.STORAGE_KEYS.COURSES)) {
      localStorage.setItem(this.STORAGE_KEYS.COURSES, JSON.stringify([]));
    }
    if (!localStorage.getItem(this.STORAGE_KEYS.PREFERENCES)) {
      localStorage.setItem(this.STORAGE_KEYS.PREFERENCES, JSON.stringify([]));
    }
    if (!localStorage.getItem(this.STORAGE_KEYS.NEXT_USER_ID)) {
      localStorage.setItem(this.STORAGE_KEYS.NEXT_USER_ID, '1');
    }
    if (!localStorage.getItem(this.STORAGE_KEYS.NEXT_COURSE_ID)) {
      localStorage.setItem(this.STORAGE_KEYS.NEXT_COURSE_ID, '1');
    }
  }

  private getUsers(): any[] {
    return JSON.parse(localStorage.getItem(this.STORAGE_KEYS.USERS) || '[]');
  }

  private saveUsers(users: any[]): void {
    localStorage.setItem(this.STORAGE_KEYS.USERS, JSON.stringify(users));
  }

  private getCourses(): any[] {
    return JSON.parse(localStorage.getItem(this.STORAGE_KEYS.COURSES) || '[]');
  }

  private saveCourses(courses: any[]): void {
    localStorage.setItem(this.STORAGE_KEYS.COURSES, JSON.stringify(courses));
  }

  private getPreferences(): any[] {
    return JSON.parse(localStorage.getItem(this.STORAGE_KEYS.PREFERENCES) || '[]');
  }

  private savePreferences(preferences: any[]): void {
    localStorage.setItem(this.STORAGE_KEYS.PREFERENCES, JSON.stringify(preferences));
  }

  private getNextUserId(): number {
    const id = parseInt(localStorage.getItem(this.STORAGE_KEYS.NEXT_USER_ID) || '1');
    localStorage.setItem(this.STORAGE_KEYS.NEXT_USER_ID, (id + 1).toString());
    return id;
  }

  private getNextCourseId(): number {
    const id = parseInt(localStorage.getItem(this.STORAGE_KEYS.NEXT_COURSE_ID) || '1');
    localStorage.setItem(this.STORAGE_KEYS.NEXT_COURSE_ID, (id + 1).toString());
    return id;
  }

  // Authentication methods
  async registerUser(userData: UserRegistration): Promise<AuthResponse> {
    try {
      const users = this.getUsers();

      // Check if username or email already exists
      const existingUser = users.find(user =>
        user.username === userData.username || user.email === userData.email
      );

      if (existingUser) {
        return { success: false, message: 'Username or email already exists' };
      }

      // Hash password using simple hash
      const passwordHash = simpleHash(userData.password);

      // Create new user
      const newUser = {
        id: this.getNextUserId(),
        username: userData.username,
        email: userData.email,
        password_hash: passwordHash,
        full_name: userData.fullName,
        department: userData.department,
        institution: userData.institution,
        designation: userData.designation,
        created_at: new Date().toISOString(),
        last_login: null
      };

      users.push(newUser);
      this.saveUsers(users);

      // Create default preferences
      const preferences = this.getPreferences();
      preferences.push({
        id: preferences.length + 1,
        user_id: newUser.id,
        default_cos_count: 5,
        default_units_count: 5,
        preferred_department: userData.department,
        preferred_level: 'undergraduate'
      });
      this.savePreferences(preferences);

      return { success: true, message: 'User registered successfully' };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, message: 'Failed to create user' };
    }
  }

  async loginUser(credentials: UserCredentials): Promise<AuthResponse> {
    try {
      const users = this.getUsers();

      // Find user by username
      const user = users.find(u => u.username === credentials.username);

      if (!user) {
        return { success: false, message: 'Invalid username or password' };
      }

      // Verify password using simple hash
      const hashedInput = simpleHash(credentials.password);
      if (hashedInput !== user.password_hash) {
        return { success: false, message: 'Invalid username or password' };
      }

      // Update last login
      user.last_login = new Date().toISOString();
      this.saveUsers(users);

      // Generate simple token
      const token = generateToken(user.id, user.username);

      const userResponse: User = {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        department: user.department,
        institution: user.institution,
        designation: user.designation,
        createdAt: user.created_at,
        lastLogin: user.last_login
      };

      return {
        success: true,
        message: 'Login successful',
        user: userResponse,
        token
      };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Login failed' };
    }
  }

  async verifyToken(token: string): Promise<{ valid: boolean; userId?: number; username?: string }> {
    return verifyToken(token);
  }

  // Course management methods
  async saveCourse(userId: number, course: Course): Promise<boolean> {
    try {
      const courses = this.getCourses();

      const newCourse = {
        id: this.getNextCourseId(),
        user_id: userId,
        course_id: course.id,
        title: course.title,
        code: course.code,
        credits: course.credits,
        course_data: JSON.stringify(course),
        saved_at: new Date().toISOString()
      };

      courses.push(newCourse);
      this.saveCourses(courses);

      return true;
    } catch (error) {
      console.error('Error saving course:', error);
      return false;
    }
  }

  async getUserCourses(userId: number): Promise<SavedCourse[]> {
    try {
      const courses = this.getCourses();
      const userCourses = courses
        .filter(course => course.user_id === userId)
        .sort((a, b) => new Date(b.saved_at).getTime() - new Date(a.saved_at).getTime());

      const savedCourses: SavedCourse[] = userCourses.map(course => ({
        ...JSON.parse(course.course_data),
        userId: course.user_id,
        savedAt: course.saved_at
      }));

      return savedCourses;
    } catch (error) {
      console.error('Error fetching courses:', error);
      return [];
    }
  }

  async getUserPreferences(userId: number): Promise<{ cosCount: number; unitsCount: number }> {
    try {
      const preferences = this.getPreferences();
      const userPref = preferences.find(pref => pref.user_id === userId);

      if (!userPref) {
        return { cosCount: 5, unitsCount: 5 };
      }

      return {
        cosCount: userPref.default_cos_count || 5,
        unitsCount: userPref.default_units_count || 5
      };
    } catch (error) {
      console.error('Error fetching preferences:', error);
      return { cosCount: 5, unitsCount: 5 };
    }
  }

  async updateUserPreferences(userId: number, cosCount: number, unitsCount: number): Promise<boolean> {
    try {
      const preferences = this.getPreferences();
      const existingIndex = preferences.findIndex(pref => pref.user_id === userId);

      if (existingIndex >= 0) {
        // Update existing preferences
        preferences[existingIndex].default_cos_count = cosCount;
        preferences[existingIndex].default_units_count = unitsCount;
      } else {
        // Create new preferences
        preferences.push({
          id: preferences.length + 1,
          user_id: userId,
          default_cos_count: cosCount,
          default_units_count: unitsCount,
          preferred_department: '',
          preferred_level: 'undergraduate'
        });
      }

      this.savePreferences(preferences);
      return true;
    } catch (error) {
      console.error('Error updating preferences:', error);
      return false;
    }
  }

  async getUserById(userId: number): Promise<User | null> {
    try {
      const users = this.getUsers();
      const user = users.find(u => u.id === userId);

      if (!user) {
        return null;
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        department: user.department,
        institution: user.institution,
        designation: user.designation,
        createdAt: user.created_at,
        lastLogin: user.last_login
      };
    } catch (error) {
      console.error('Error fetching user:', error);
      return null;
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
export default databaseService;
