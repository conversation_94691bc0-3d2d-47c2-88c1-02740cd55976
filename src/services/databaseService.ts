import sqlite3 from 'sqlite3';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Course } from '../types/course';

// Database interface
export interface User {
  id: number;
  username: string;
  email: string;
  fullName: string;
  department: string;
  institution: string;
  designation: string;
  createdAt: string;
  lastLogin?: string;
}

export interface UserCredentials {
  username: string;
  password: string;
}

export interface UserRegistration {
  username: string;
  email: string;
  password: string;
  fullName: string;
  department: string;
  institution: string;
  designation: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  token?: string;
}

export interface SavedCourse extends Course {
  userId: number;
  savedAt: string;
}

class DatabaseService {
  private db: sqlite3.Database;
  private JWT_SECRET = 'course-design-system-secret-key-2024';

  constructor() {
    // Initialize SQLite database
    this.db = new sqlite3.Database('./course_system.db', (err) => {
      if (err) {
        console.error('Error opening database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.initializeTables();
      }
    });
  }

  private initializeTables(): void {
    // Create users table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        department TEXT NOT NULL,
        institution TEXT NOT NULL,
        designation TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )
    `);

    // Create courses table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS courses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        course_id TEXT NOT NULL,
        title TEXT NOT NULL,
        code TEXT NOT NULL,
        credits INTEGER NOT NULL,
        course_data TEXT NOT NULL,
        saved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Create user preferences table
    this.db.run(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        default_cos_count INTEGER DEFAULT 5,
        default_units_count INTEGER DEFAULT 5,
        preferred_department TEXT,
        preferred_level TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);
  }

  // Authentication methods
  async registerUser(userData: UserRegistration): Promise<AuthResponse> {
    return new Promise((resolve) => {
      // Check if username or email already exists
      this.db.get(
        'SELECT id FROM users WHERE username = ? OR email = ?',
        [userData.username, userData.email],
        async (err, row) => {
          if (err) {
            resolve({ success: false, message: 'Database error occurred' });
            return;
          }

          if (row) {
            resolve({ success: false, message: 'Username or email already exists' });
            return;
          }

          // Hash password
          const saltRounds = 10;
          const passwordHash = await bcrypt.hash(userData.password, saltRounds);

          // Insert new user
          this.db.run(
            `INSERT INTO users (username, email, password_hash, full_name, department, institution, designation)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              userData.username,
              userData.email,
              passwordHash,
              userData.fullName,
              userData.department,
              userData.institution,
              userData.designation
            ],
            function(err) {
              if (err) {
                resolve({ success: false, message: 'Failed to create user' });
                return;
              }

              // Create default preferences
              const userId = this.lastID;
              resolve({ success: true, message: 'User registered successfully' });
            }
          );
        }
      );
    });
  }

  async loginUser(credentials: UserCredentials): Promise<AuthResponse> {
    return new Promise((resolve) => {
      this.db.get(
        'SELECT * FROM users WHERE username = ?',
        [credentials.username],
        async (err, row: any) => {
          if (err) {
            resolve({ success: false, message: 'Database error occurred' });
            return;
          }

          if (!row) {
            resolve({ success: false, message: 'Invalid username or password' });
            return;
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(credentials.password, row.password_hash);
          if (!isValidPassword) {
            resolve({ success: false, message: 'Invalid username or password' });
            return;
          }

          // Update last login
          this.db.run(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
            [row.id]
          );

          // Generate JWT token
          const token = jwt.sign(
            { userId: row.id, username: row.username },
            this.JWT_SECRET,
            { expiresIn: '7d' }
          );

          const user: User = {
            id: row.id,
            username: row.username,
            email: row.email,
            fullName: row.full_name,
            department: row.department,
            institution: row.institution,
            designation: row.designation,
            createdAt: row.created_at,
            lastLogin: row.last_login
          };

          resolve({
            success: true,
            message: 'Login successful',
            user,
            token
          });
        }
      );
    });
  }

  async verifyToken(token: string): Promise<{ valid: boolean; userId?: number; username?: string }> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;
      return { valid: true, userId: decoded.userId, username: decoded.username };
    } catch (error) {
      return { valid: false };
    }
  }

  // Course management methods
  async saveCourse(userId: number, course: Course): Promise<boolean> {
    return new Promise((resolve) => {
      this.db.run(
        `INSERT INTO courses (user_id, course_id, title, code, credits, course_data)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          userId,
          course.id,
          course.title,
          course.code,
          course.credits,
          JSON.stringify(course)
        ],
        function(err) {
          if (err) {
            console.error('Error saving course:', err);
            resolve(false);
            return;
          }
          resolve(true);
        }
      );
    });
  }

  async getUserCourses(userId: number): Promise<SavedCourse[]> {
    return new Promise((resolve) => {
      this.db.all(
        'SELECT * FROM courses WHERE user_id = ? ORDER BY saved_at DESC',
        [userId],
        (err, rows: any[]) => {
          if (err) {
            console.error('Error fetching courses:', err);
            resolve([]);
            return;
          }

          const courses: SavedCourse[] = rows.map(row => ({
            ...JSON.parse(row.course_data),
            userId: row.user_id,
            savedAt: row.saved_at
          }));

          resolve(courses);
        }
      );
    });
  }

  async getUserPreferences(userId: number): Promise<{ cosCount: number; unitsCount: number }> {
    return new Promise((resolve) => {
      this.db.get(
        'SELECT default_cos_count, default_units_count FROM user_preferences WHERE user_id = ?',
        [userId],
        (err, row: any) => {
          if (err || !row) {
            // Return defaults if no preferences found
            resolve({ cosCount: 5, unitsCount: 5 });
            return;
          }

          resolve({
            cosCount: row.default_cos_count || 5,
            unitsCount: row.default_units_count || 5
          });
        }
      );
    });
  }

  async updateUserPreferences(userId: number, cosCount: number, unitsCount: number): Promise<boolean> {
    return new Promise((resolve) => {
      // First try to update existing preferences
      this.db.run(
        'UPDATE user_preferences SET default_cos_count = ?, default_units_count = ? WHERE user_id = ?',
        [cosCount, unitsCount, userId],
        function(err) {
          if (err) {
            resolve(false);
            return;
          }

          // If no rows were updated, insert new preferences
          if (this.changes === 0) {
            this.db.run(
              'INSERT INTO user_preferences (user_id, default_cos_count, default_units_count) VALUES (?, ?, ?)',
              [userId, cosCount, unitsCount],
              (err) => {
                resolve(!err);
              }
            );
          } else {
            resolve(true);
          }
        }
      );
    });
  }

  async getUserById(userId: number): Promise<User | null> {
    return new Promise((resolve) => {
      this.db.get(
        'SELECT * FROM users WHERE id = ?',
        [userId],
        (err, row: any) => {
          if (err || !row) {
            resolve(null);
            return;
          }

          const user: User = {
            id: row.id,
            username: row.username,
            email: row.email,
            fullName: row.full_name,
            department: row.department,
            institution: row.institution,
            designation: row.designation,
            createdAt: row.created_at,
            lastLogin: row.last_login
          };

          resolve(user);
        }
      );
    });
  }

  // Close database connection
  close(): void {
    this.db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('Database connection closed');
      }
    });
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
export default databaseService;
