import { LLMService, LLMConfig } from './llmService';
import { ChatMessage } from './chatbotService';

// LangChain imports for enhanced chatbot features
import { ConversationChain } from 'langchain/chains';
import { BufferMemory, ConversationSummaryMemory } from 'langchain/memory';
import { PromptTemplate } from '@langchain/core/prompts';
import { BaseLanguageModel } from '@langchain/core/language_models/base';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';

export interface EnhancedChatbotConfig {
  llmConfig: LLMConfig;
  memoryType?: 'buffer' | 'summary';
  maxTokenLimit?: number;
  conversationSummaryTokens?: number;
}

export class LangChainChatbotService {
  private llm: BaseLanguageModel;
  private memory: BufferMemory | ConversationSummaryMemory;
  private conversationChain: ConversationChain;
  private config: EnhancedChatbotConfig;

  constructor(config: EnhancedChatbotConfig) {
    this.config = {
      memoryType: 'buffer',
      maxTokenLimit: 4000,
      conversationSummaryTokens: 1000,
      ...config
    };

    // Initialize LLM
    this.llm = this.initializeLLM();

    // Initialize memory
    this.memory = this.initializeMemory();

    // Initialize conversation chain
    this.conversationChain = this.initializeConversationChain();
  }

  private initializeLLM(): BaseLanguageModel {
    const { provider, apiKey, model, temperature = 0.7, maxTokens = 4000 } = this.config.llmConfig;

    switch (provider) {
      case 'openai':
        return new ChatOpenAI({
          openAIApiKey: apiKey,
          modelName: model || 'gpt-4-turbo-preview',
          temperature,
          maxTokens,
        });

      case 'anthropic':
        return new ChatAnthropic({
          anthropicApiKey: apiKey,
          modelName: model || 'claude-3-sonnet-20240229',
          temperature,
          maxTokens,
        });

      case 'google':
        return new ChatGoogleGenerativeAI({
          apiKey: apiKey,
          modelName: model || 'gemini-pro',
          temperature,
          maxTokens,
        });

      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  private initializeMemory(): BufferMemory | ConversationSummaryMemory {
    if (this.config.memoryType === 'summary') {
      return new ConversationSummaryMemory({
        llm: this.llm,
        maxTokenLimit: this.config.conversationSummaryTokens,
        returnMessages: true,
      });
    } else {
      return new BufferMemory({
        returnMessages: true,
        memoryKey: 'history',
      });
    }
  }

  private initializeConversationChain(): ConversationChain {
    const prompt = PromptTemplate.fromTemplate(`
You are a professional Course Design Assistant specializing in Indian higher education. Your expertise includes:

**Core Competencies:**
- AICTE (All India Council for Technical Education) guidelines and compliance
- UGC (University Grants Commission) standards and frameworks
- NEP 2020 (National Education Policy) implementation
- Bloom's Taxonomy and learning outcome design
- Indian university credit systems and assessment patterns
- Curriculum design best practices
- Industry-academia alignment
- Academic quality assurance

**Communication Style:**
- Professional and knowledgeable
- Provide specific, actionable advice
- Reference official guidelines when relevant
- Use clear, academic language
- Be helpful and supportive
- Acknowledge limitations when appropriate

**Key Areas of Assistance:**
1. Course structure and credit allocation
2. Learning objectives and outcomes design
3. Assessment pattern recommendations
4. AICTE compliance requirements
5. Unit planning and topic organization
6. Textbook and resource selection
7. Industry relevance and skill development
8. Academic calendar and scheduling

**Response Guidelines:**
- Keep responses concise but comprehensive
- Provide practical examples when helpful
- Reference specific AICTE/UGC guidelines when applicable
- Suggest next steps or follow-up actions
- Maintain professional academic tone
- If unsure about specific regulations, recommend consulting official sources

Current conversation:
{history}

Human: {input}
Assistant:`);

    return new ConversationChain({
      llm: this.llm,
      memory: this.memory,
      prompt,
      verbose: false,
    });
  }

  async generateResponse(userMessage: string): Promise<string> {
    try {
      const response = await this.conversationChain.call({
        input: userMessage,
      });

      return response.response || response.text || 'I apologize, but I encountered an issue generating a response. Please try again.';
    } catch (error) {
      console.error('Error generating LangChain response:', error);

      // Fallback to a simple response
      return this.generateFallbackResponse(userMessage);
    }
  }

  private generateFallbackResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      return 'Hello! I\'m your Course Design Assistant. I can help you with AICTE guidelines, curriculum design, learning outcomes, and assessment patterns. What would you like to know?';
    }

    if (lowerMessage.includes('help')) {
      return 'I can assist you with course design, AICTE compliance, learning outcomes, assessment patterns, and curriculum development. What specific topic would you like to explore?';
    }

    return 'I\'m here to help with course design and curriculum development. Could you please rephrase your question or ask about a specific aspect of course design?';
  }

  async clearMemory(): Promise<void> {
    await this.memory.clear();
  }

  async getMemoryVariables(): Promise<Record<string, any>> {
    return await this.memory.loadMemoryVariables({});
  }

  // Method to get conversation summary (useful for summary memory)
  async getConversationSummary(): Promise<string> {
    if (this.memory instanceof ConversationSummaryMemory) {
      const variables = await this.memory.loadMemoryVariables({});
      return variables.history || 'No conversation history available.';
    } else {
      const variables = await this.memory.loadMemoryVariables({});
      return `Recent conversation: ${variables.history || 'No history available.'}`;
    }
  }

  // Method to export conversation for analysis
  async exportConversation(): Promise<ChatMessage[]> {
    const variables = await this.memory.loadMemoryVariables({});
    const history = variables.history || '';

    // Parse the history into ChatMessage format
    const messages: ChatMessage[] = [];
    const lines = history.split('\n').filter(line => line.trim());

    lines.forEach((line, index) => {
      if (line.startsWith('Human:')) {
        messages.push({
          id: `msg_${index}`,
          text: line.replace('Human:', '').trim(),
          sender: 'user',
          timestamp: new Date(),
        });
      } else if (line.startsWith('Assistant:')) {
        messages.push({
          id: `msg_${index}`,
          text: line.replace('Assistant:', '').trim(),
          sender: 'bot',
          timestamp: new Date(),
        });
      }
    });

    return messages;
  }
}