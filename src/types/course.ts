export interface CourseUnit {
  unitNumber: number;
  title: string;
  topics: string[];
  topicDescriptions?: { [key: string]: string };
  learningOutcomes: string[];
  contactHours: number;
  practicalHours?: number;
  assignments?: string[];
  practicalExercises?: string[];
  caseStudies?: string[];
}

export interface CourseObjective {
  id: number;
  description: string;
}

export interface CourseOutcome {
  id: string;
  description: string;
  bloomsLevel: string;
}

export interface TextBook {
  title: string;
  authors: string[];
  publisher: string;
  year: number;
  isbn?: string;
}

export interface IndustryRelevance {
  skills: string[];
  tools: string[];
  certifications: string[];
  careerOpportunities: string[];
}

export interface Course {
  id: string;
  title: string;
  code: string;
  credits: number;
  contactHours: {
    lecture: number;
    tutorial: number;
    practical: number;
  };
  prerequisites: string[];
  corequisites?: string[];
  objectives: CourseObjective[];
  outcomes: CourseOutcome[];
  units: CourseUnit[];
  textBooks: TextBook[];
  referenceBooks: TextBook[];
  onlineResources: string[];
  assessmentPattern: {
    internalAssessment: number;
    endSemExam: number;
    practical?: number;
  };
  industryRelevance?: IndustryRelevance;
  marketDemand?: 'high' | 'medium' | 'low';
  difficultyLevel?: number;
  createdAt: Date;
  generatedBy: string;
}

export interface CourseGenerationRequest {
  subject: string;
  level: 'undergraduate' | 'postgraduate';
  semester: number;
  duration: number;
  courseType: 'theory' | 'practical' | 'theory-practical';
  department: string;
  specialization?: string;
}